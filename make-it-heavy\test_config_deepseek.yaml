agent:
  max_iterations: 5
deepseek:
  api_key: test-key
  base_url: https://api.deepseek.com
  model: deepseek-chat
orchestrator:
  aggregation_strategy: consensus
  parallel_agents: 2
  question_generation_prompt: 'Generate {num_agents} questions for: {user_input}'
  synthesis_prompt: 'Synthesize these {num_responses} responses: {agent_responses}'
  task_timeout: 30
provider:
  type: deepseek
system_prompt: You are a helpful assistant

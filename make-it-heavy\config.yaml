agent:
  max_iterations: 10
openrouter:
  api_key: 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
  base_url: https://openrouter.ai/api/v1
  model: openrouter/horizon-alpha
orchestrator:
  aggregation_strategy: consensus
  parallel_agents: 4
  question_generation_prompt: 'You are an orchestrator that needs to create {num_agents}
    different questions to thoroughly analyze this topic from multiple angles.


    Original user query: {user_input}


    Generate exactly {num_agents} different, specific questions that will help gather
    comprehensive information about this topic.

    Each question should approach the topic from a different angle (research, analysis,
    verification, alternatives, etc.).


    Return your response as a JSON array of strings, like this:

    ["question 1", "question 2", "question 3", "question 4"]


    Only return the JSON array, nothing else.'
  synthesis_prompt: 'You have {num_responses} different AI agents that analyzed the
    same query from different perspectives.

    Your job is to synthesize their responses into ONE comprehensive final answer.


    Here are all the agent responses:


    {agent_responses}


    IMPORTANT: Just synthesize these into ONE final comprehensive answer that combines
    the best information from all agents.

    Do NOT call mark_task_complete or any other tools. Do NOT mention that you are
    synthesizing multiple responses.

    Simply provide the final synthesized answer directly as your response.'
  task_timeout: 300
provider:
  type: openrouter
search:
  max_results: 5
  user_agent: Mozilla/5.0 (compatible; Clipboard Agent)
system_prompt: You are a helpful assistant.

#!/usr/bin/env python3
"""
Clipboard LLM Automator

A GUI application that monitors the system clipboard for text and image changes.
When changes are detected, the clipboard content is sent to a selected Large Language Model (LLM)
via appropriate APIs (OpenAI, Anthropic, Google Gemini, Ollama, OpenRouter).
The LLM response is then copied back to the clipboard and optionally displayed
in an "Always on Top" window.

Author: [Your Name]
Version: 1.0.0
License: MIT
"""

import customtkinter
import tkinter as tk # Keep for messagebox, vars, etc.
from tkinter import scrolledtext, messagebox # Keep messagebox, scrolledtext for now
import pyperclip
import threading
import time
import json
import os
import openai
import anthropic
import google.generativeai as genai
import google.generativeai.types as genai_types # Dla obiektu Part
import requests # Dla Ollama
import platform
import winsound # Dla dźwięku powiadomienia w Windows
import sys # Do bardziej niezawodnego sprawdzania platformy
from PIL import ImageGrab, Image # Do obsługi obrazów ze schowka
import io # Do obsługi danych obrazu w pamięci
import base64 # Do kodowania obrazów dla API
import hashlib # Do hashowania obrazów w celu wykrywania zmian
import yaml # Do obsługi plików konfiguracyjnych agentów
import subprocess # Do uruchamiania agentów
# Set CustomTkinter appearance
customtkinter.set_appearance_mode("System") # Modes: "System" (default), "Dark", "Light"
customtkinter.set_default_color_theme("blue") # Themes: "blue" (default), "green", "dark-blue"
CONFIG_FILE = "config.json"
NUM_API_KEYS = 3 # Define the number of keys per provider
NUM_SYSTEM_PROMPTS = 5 # Define the number of system prompt slots

# Constants for error messages in model list
ERROR_FETCHING = "Błąd pobierania modeli"
ERROR_OLLAMA_CONNECTION = "Błąd połączenia z Ollama"
NO_MODELS_FOUND = "Brak modeli"
ERROR_GUI = "Błąd GUI"
ERROR_NO_API_KEY_TEMPLATE = "Wprowadź klucz API (Slot {slot})" # Template for API key error
class ClipboardLLMAutomator:
    def __init__(self, root: customtkinter.CTk): # Type hint for clarity
        self.root = root
        self.root.title("Clipboard LLM Automator")
        # Increased height to accommodate more prompts
        self.root.geometry("1200x950") # Doubled width, kept increased height

        self.llm_providers = ["OpenAI", "Anthropic", "Google Gemini", "Ollama (Local)", "OpenRouter", "DeepSeek"] # Added OpenRouter and DeepSeek
        # Restore global provider/model selection variables
        self.selected_provider = tk.StringVar(value=self.llm_providers[0])
        self.model_name = tk.StringVar()

        # Per-prompt provider and model selection - Initialize with global defaults
        self.prompt_providers = [tk.StringVar(value=self.selected_provider.get()) for _ in range(NUM_SYSTEM_PROMPTS)]
        self.prompt_models = [tk.StringVar(value=self.model_name.get()) for _ in range(NUM_SYSTEM_PROMPTS)]
        self.prompt_provider_combos = [None] * NUM_SYSTEM_PROMPTS
        self.prompt_model_combos = [None] * NUM_SYSTEM_PROMPTS
        self.api_keys = {} # Dictionary to store API keys {provider: [key1, key2, key3]} - Ensure list always has NUM_API_KEYS elements
        self.current_api_key = tk.StringVar() # Holds the API key currently shown/edited in the GUI for the selected provider/slot
        self.selected_api_key_index = tk.IntVar(value=0) # Index of the selected key slot (0, 1, 2)
        # Initialize list for system prompts based on NUM_SYSTEM_PROMPTS
        self.system_prompts = [tk.StringVar() for _ in range(NUM_SYSTEM_PROMPTS)]
        self.active_prompt_index = tk.IntVar(value=0) # Index of the active prompt

        self.is_monitoring = False
        self.monitoring_thread = None
        self.last_clipboard_content = ""
        self.last_clipboard_image_hash = None # Hash ostatnio przetworzonego obrazu
        # Always on Top window variables
        self.always_on_top_var = tk.BooleanVar(value=False)
        self.on_top_window = None
        self.on_top_text_widget = None
        self.on_top_window_width = 400 # Domyślna szerokość
        self.on_top_window_height = 600 # Domyślna (zwiększona) wysokość
        self.create_widgets()
        self.load_config() # Load config might set always_on_top_var, so call toggle after load if needed

        # Handle main window closing
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)


    def create_widgets(self):
        # --- Top Frame (contains Config and Query) ---
        top_frame = customtkinter.CTkFrame(self.root, fg_color="transparent") # Główny kontener górny
        top_frame.pack(padx=10, pady=10, fill="x", expand=False) # Nie rozszerzaj pionowo
        top_frame.grid_columnconfigure(0, weight=1) # Kolumna dla config_frame (1/3)
        top_frame.grid_columnconfigure(1, weight=2) # Kolumna dla query_frame (2/3)
        top_frame.grid_rowconfigure(0, weight=1) # Pozwól wierszowi się rozciągać

        # --- Configuration Frame (inside top_frame) ---
        config_frame = customtkinter.CTkFrame(top_frame) # Zmieniono rodzica na top_frame
        config_frame.grid(row=0, column=0, padx=(0, 5), pady=0, sticky="nsew") # Umieszczono w siatce top_frame
        # Add a label inside the CTkFrame to mimic LabelFrame text
        config_label = customtkinter.CTkLabel(config_frame, text="Konfiguracja LLM", fg_color="transparent", font=customtkinter.CTkFont(weight="bold"))
        config_label.grid(row=0, column=0, columnspan=3, padx=10, pady=(5, 10), sticky="w") # Span across columns

        # --- Global Provider/Model Selection ---
        customtkinter.CTkLabel(config_frame, text="Dostawca LLM (Globalny):", fg_color="transparent").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        provider_menu = customtkinter.CTkComboBox(config_frame, variable=self.selected_provider, values=self.llm_providers, state="readonly", width=200, command=self.on_provider_change)
        provider_menu.grid(row=1, column=1, columnspan=2, padx=5, pady=5, sticky="ew")

        customtkinter.CTkLabel(config_frame, text="Model (Globalny):", fg_color="transparent").grid(row=2, column=0, padx=5, pady=5, sticky="w")
        # Ensure model_combobox uses variable and starts as readonly
        self.model_combobox = customtkinter.CTkComboBox(config_frame, variable=self.model_name, state="readonly", width=200, command=self.on_global_model_change) # This is the GLOBAL one now + Added command
        self.model_combobox.grid(row=2, column=1, padx=5, pady=5, sticky="ew")
        # Add a refresh button next to the GLOBAL model list
        refresh_button = customtkinter.CTkButton(config_frame, text="🔄", command=self.update_models_list, width=30) # CTk width is in pixels
        refresh_button.grid(row=2, column=2, padx=(0, 5), pady=5, sticky="w")

        # --- API Key Management (Adjust rows) ---


        # API Key Management (Multiple Keys)
        # Adjust grid rows
        # Replace ttk.Label with customtkinter.CTkLabel
        self.api_key_label = customtkinter.CTkLabel(config_frame, text="Wybierz/Edytuj Klucz API:", fg_color="transparent")
        self.api_key_label.grid(row=3, column=0, padx=5, pady=5, sticky="w") # Move down to row 3

        # Combobox to select which API key slot to view/edit
        key_slot_values = [f"Klucz {i+1}" for i in range(NUM_API_KEYS)]
        # Replace ttk.Combobox with customtkinter.CTkComboBox
        self.api_key_selector_combo = customtkinter.CTkComboBox(
            config_frame,
            values=key_slot_values,
            state="readonly",
            width=100, # CTk width in pixels
            command=self.on_key_selection_change # Use command
        )
        # self.api_key_selector_combo.set(key_slot_values[0]) # Set initial display - Comment out again
        # Adjust grid rows
        self.api_key_selector_combo.grid(row=3, column=1, padx=5, pady=5, sticky="w") # Move down to row 3
        # self.api_key_selector_combo.bind("<<ComboboxSelected>>", self.on_key_selection_change) # Use command

        # Entry to display/edit the selected API key
        # Replace ttk.Entry with customtkinter.CTkEntry
        self.api_key_editor_entry = customtkinter.CTkEntry(config_frame, textvariable=self.current_api_key, show="*", width=200) # Adjust width if needed
        # Adjust grid rows
        self.api_key_editor_entry.grid(row=4, column=0, columnspan=2, padx=5, pady=5, sticky="ew") # Move down to row 4

        # Button to explicitly save the key in the editor entry to the selected slot
        # Replace ttk.Button with customtkinter.CTkButton
        self.save_api_key_button = customtkinter.CTkButton(config_frame, text="Zapisz Klucz", command=self.save_selected_api_key, width=100) # Adjust width
        # Adjust grid rows
        self.save_api_key_button.grid(row=4, column=2, padx=(0, 5), pady=5, sticky="w") # Move down to row 4

        config_frame.columnconfigure(1, weight=1) # Make combobox/entry expand
        self.selected_provider.trace_add("write", self.on_provider_change) # Restore trace for global provider
        self.update_api_key_visibility() # Set initial visibility
        # self.update_models_list() # Removed initial call here, load_config will handle it

        # --- Query Frame (inside top_frame) ---
        query_frame = customtkinter.CTkFrame(top_frame)
        query_frame.grid(row=0, column=1, padx=(5, 0), pady=0, sticky="nsew")
        query_frame.grid_rowconfigure(1, weight=1) # Pozwól polu tekstowemu rosnąć
        query_frame.grid_columnconfigure(0, weight=1) # Pozwól polu tekstowemu rosnąć

        query_label = customtkinter.CTkLabel(query_frame, text="ZAPYTANIE - Enter wyślij, Shift+Enter nowa linia", fg_color="transparent", font=customtkinter.CTkFont(weight="bold"))
        query_label.grid(row=0, column=0, padx=10, pady=(5, 5), sticky="w")

        self.query_textbox = customtkinter.CTkTextbox(query_frame, wrap=tk.WORD)
        self.query_textbox.grid(row=1, column=0, padx=10, pady=(0, 10), sticky="nsew")
        self.query_textbox.bind("<KeyPress>", self.handle_query_keypress) # Powiązanie zdarzenia

        # --- Prompts Frame ---
        # Replace ttk.LabelFrame with customtkinter.CTkFrame
        prompts_frame = customtkinter.CTkFrame(self.root)
        prompts_frame.pack(padx=10, pady=5, fill="both", expand=True)
        # Add a label inside the CTkFrame
        prompts_label = customtkinter.CTkLabel(prompts_frame, text="Prompty Systemowe", fg_color="transparent", font=customtkinter.CTkFont(weight="bold"))
        prompts_label.grid(row=0, column=0, columnspan=3, padx=10, pady=(5, 10), sticky="w") # Span across 3 columns now

        # Create NUM_SYSTEM_PROMPTS prompt slots
        for i in range(NUM_SYSTEM_PROMPTS):
            # Adjust grid rows
            # Replace ttk.Radiobutton with customtkinter.CTkRadioButton
            # Remove text from Radiobutton
            # Usunięto CTkRadioButton

            # Replace scrolledtext with customtkinter.CTkTextbox
            # Note: CTkTextbox might require different handling/attributes than scrolledtext
            # Create inner frame for combos
            combo_frame = customtkinter.CTkFrame(prompts_frame, fg_color="transparent")
            combo_frame.grid(row=i*2+1, rowspan=2, column=0, padx=5, pady=5, sticky="nsew") # Przesunięto do kolumny 0

            # Text Area - place in column 2, span 2 rows
            text_area = customtkinter.CTkTextbox(prompts_frame, wrap=tk.WORD, height=100) # Adjust height as needed, width will expand
            text_area.grid(row=i*2+1, rowspan=2, column=1, padx=5, pady=5, sticky="nsew") # Przesunięto do kolumny 1
            # Store text area widgets to easily get/set text later
            setattr(self, f"prompt_text_{i}", text_area)
            # Upewnienie się, że powiązanie kliknięcia istnieje
            text_area.bind("<Button-1>", lambda event, idx=i: self.set_active_prompt(idx))

            # Add Provider ComboBox for this prompt slot
            prompt_provider_combo = customtkinter.CTkComboBox(
                combo_frame, # Zmieniono rodzica na combo_frame
                variable=self.prompt_providers[i],
                values=self.llm_providers,
                state="readonly",
                width=150,
                command=lambda idx=i: self.on_prompt_provider_change(idx) # Pass index to command
            )
            # Place provider combo inside combo_frame
            prompt_provider_combo.grid(row=0, column=0, padx=0, pady=(0, 5), sticky="ew") # Top row in inner frame
            self.prompt_provider_combos[i] = prompt_provider_combo

            # Add Model ComboBox for this prompt slot
            prompt_model_combo = customtkinter.CTkComboBox(
                combo_frame, # Zmieniono rodzica na combo_frame
                variable=self.prompt_models[i],
                values=[""], # Initially empty, will be populated later
                state="disabled", # Start disabled
                width=150
            )
            # Place model combo inside combo_frame, below provider
            prompt_model_combo.grid(row=1, column=0, padx=0, pady=0, sticky="ew") # Bottom row in inner frame
            self.prompt_model_combos[i] = prompt_model_combo
            # Configure row weights for expansion
            # Adjust grid rows (configure rows starting from 1)
            # Configure row weights for expansion (use i*2+1 and i*2+2)
            prompts_frame.rowconfigure(i*2+1, weight=1) # Let the rows containing content expand
            prompts_frame.rowconfigure(i*2+2, weight=1)


        # Configure column weights
        # Configure column weights
        prompts_frame.columnconfigure(0, weight=0) # Combo frame fixed width
        prompts_frame.columnconfigure(1, weight=1) # Text area expands horizontally
        # Row weights are now configured in the loop above

        # --- Control Frame ---
        # Replace ttk.Frame with customtkinter.CTkFrame
        control_frame = customtkinter.CTkFrame(self.root, fg_color="transparent") # Make it transparent like default ttk.Frame
        control_frame.pack(padx=10, pady=10, fill="x")

        # Replace ttk.Button with customtkinter.CTkButton
        self.start_stop_button = customtkinter.CTkButton(control_frame, text="Start Monitorowania", command=self.toggle_monitoring)
        self.start_stop_button.pack(side=tk.LEFT, padx=5)

        # Always on Top Checkbox
        # Replace ttk.Checkbutton with customtkinter.CTkCheckBox
        self.always_on_top_check = customtkinter.CTkCheckBox(
            control_frame,
            text="Zawsze na wierzchu (Okno odpowiedzi)",
            variable=self.always_on_top_var,
            command=self.toggle_on_top_window,
            onvalue=True, offvalue=False # Explicitly set on/off values
        )
        self.always_on_top_check.pack(side=tk.LEFT, padx=10)


        # Replace ttk.Button with customtkinter.CTkButton
        save_button = customtkinter.CTkButton(control_frame, text="Zapisz Konfigurację", command=self.save_config)
        save_button.pack(side=tk.RIGHT, padx=5)

    def _ensure_api_keys_list(self, provider):
        """Ensures the API key list for the provider exists and has NUM_API_KEYS elements."""
        if provider not in self.api_keys:
            self.api_keys[provider] = [""] * NUM_API_KEYS
        else:
            current_keys = self.api_keys[provider]
            if not isinstance(current_keys, list): # Handle potential data corruption
                 current_keys = [""]
            # Ensure list has exactly NUM_API_KEYS elements
            if len(current_keys) < NUM_API_KEYS:
                current_keys.extend([""] * (NUM_API_KEYS - len(current_keys)))
            elif len(current_keys) > NUM_API_KEYS:
                current_keys = current_keys[:NUM_API_KEYS]
            self.api_keys[provider] = current_keys
        return self.api_keys[provider]

    def on_key_selection_change(self, event=None):
        """Called when the selected API key slot changes."""
        try:
            selected_display_value = self.api_key_selector_combo.get() # e.g., "Klucz 1"
            # Convert display value back to index (0-based)
            self.selected_api_key_index.set(int(selected_display_value.split(" ")[1]) - 1)
        except (ValueError, IndexError):
             self.selected_api_key_index.set(0) # Default to first key on error
             self.api_key_selector_combo.set("Klucz 1")

        index = self.selected_api_key_index.get()
        # Use the global provider for API key context
        provider = self.selected_provider.get()
        keys_list = self._ensure_api_keys_list(provider)

        if 0 <= index < len(keys_list):
            self.current_api_key.set(keys_list[index])
        else:
            self.current_api_key.set("") # Should not happen with _ensure_api_keys_list

    def save_selected_api_key(self):
        """Saves the key from the editor entry to the currently selected slot."""
        # Use the global provider for API key context
        provider = self.selected_provider.get()
        if provider == "Ollama (Local)": # No keys for Ollama
            return

        index = self.selected_api_key_index.get()
        entered_key = self.current_api_key.get()
        keys_list = self._ensure_api_keys_list(provider)

        if 0 <= index < NUM_API_KEYS:
            keys_list[index] = entered_key
            self.api_keys[provider] = keys_list # Update the dictionary
            print(f"Zapisano klucz w slocie {index + 1} dla {provider}.")
            # Optionally trigger model refresh if key was entered and needed
            if provider not in ["Ollama (Local)", "Anthropic"] and entered_key:
                 # Check if the current model indicates an error state related to API keys
                 error_messages = [f"Wprowadź klucz API (Slot {i+1})" for i in range(NUM_API_KEYS)]
                 # Check the global model name
                 current_global_model = self.model_name.get()
                 print(f"[DEBUG] save_selected_api_key: Sprawdzanie czy '{current_global_model}' jest w {error_messages}")
                 if current_global_model in error_messages:
                      print("Wykryto zapisanie klucza API, odświeżanie modeli...")
                      # Trigger update for the specific prompt's model list
                      # Trigger update for the global model list
                      self.update_models_list()
        else:
             print(f"Błąd: Nieprawidłowy indeks slotu klucza ({index})")

    def get_selected_api_key(self):
        """Gets the API key from the currently selected slot for the current provider."""
        provider = self.selected_provider.get()
        if provider == "Ollama (Local)":
            return None # Ollama doesn't use keys

        index = self.selected_api_key_index.get()
        keys_list = self._ensure_api_keys_list(provider)

        if 0 <= index < len(keys_list):
            return keys_list[index]
        else:
            print(f"Ostrzeżenie: Nieprawidłowy indeks ({index}) wybranego klucza dla {provider}, używanie pierwszego klucza.")
            return keys_list[0] if keys_list else None # Fallback to first key or None

    def update_api_key_visibility(self, event=None):
        """Show API key management widgets only if the provider is not Ollama."""
        # Use the global provider for visibility check
        provider = self.selected_provider.get()
        widgets_to_toggle = [
            self.api_key_label,
            self.api_key_selector_combo,
            self.api_key_editor_entry,
            self.save_api_key_button
        ]
        if provider == "Ollama (Local)":
            for widget in widgets_to_toggle:
                widget.grid_remove()
        else:
            # Ensure they are placed correctly if re-enabled
            self.api_key_label.grid()
            self.api_key_selector_combo.grid()
            self.api_key_editor_entry.grid()
            self.save_api_key_button.grid()

    def get_active_prompt(self):
        """Returns the text from the currently selected system prompt text area."""
        index = self.active_prompt_index.get()
        # Ensure index is within valid range for safety, though radio buttons should handle it
        if 0 <= index < NUM_SYSTEM_PROMPTS:
            text_area = getattr(self, f"prompt_text_{index}", None)
            if text_area: # CTkTextbox uses .get("0.0", "end")
                return text_area.get("0.0", "end").strip()
        return ""

    def set_prompts_text(self, prompts_list):
        """Sets the text for the prompt text areas."""
        # Ensure the input list has the correct number of elements
        if not isinstance(prompts_list, list):
            prompts_list = [""] * NUM_SYSTEM_PROMPTS
        if len(prompts_list) < NUM_SYSTEM_PROMPTS:
            prompts_list.extend([""] * (NUM_SYSTEM_PROMPTS - len(prompts_list)))
        elif len(prompts_list) > NUM_SYSTEM_PROMPTS:
            prompts_list = prompts_list[:NUM_SYSTEM_PROMPTS]

        for i, prompt_text in enumerate(prompts_list):
             # Check if the attribute exists before accessing (safer)
             text_area = getattr(self, f"prompt_text_{i}", None)
             if text_area:
                 text_area.delete("0.0", "end") # CTkTextbox uses "0.0"
                 text_area.insert("0.0", prompt_text) # CTkTextbox uses "0.0"

    def on_provider_change(self, *args):
        """Called when the selected provider changes."""
        provider = self.selected_provider.get()
        self.update_api_key_visibility()
        self.model_name.set("") # Clear previous model selection
        self.model_combobox['values'] = [] # Clear combobox values

        # Reset API key selector and load the first key for the new provider
        self.api_key_selector_combo.set("Klucz 1")
        self.selected_api_key_index.set(0)
        keys_list = self._ensure_api_keys_list(provider)
        self.current_api_key.set(keys_list[0]) # Display the first key

        # Check if API key is needed and missing *before* attempting to update models
        # Use the potentially updated key (first key for the new provider)
        current_key = self.get_selected_api_key() # Use the helper to get the currently selected key (which is now the first one)
        error_key_msg = f"Wprowadź klucz API (Slot {self.selected_api_key_index.get()+1})"
        if provider not in ["Ollama (Local)", "Anthropic"] and not current_key:
            self.model_combobox['values'] = [error_key_msg]
            self.model_name.set(error_key_msg)
            self.model_combobox.configure(state="disabled") # Use string "disabled" for CTk state
            return # Don't fetch models yet

        # If key not needed or present, proceed with updating the model list
        # If key not needed or present, proceed with updating the model list
        print("Rozpoczęcie aktualizacji listy modeli globalnych...")
        self.update_models_list() # This will eventually set self.model_name if successful
        models_updated = True # Zakładamy, że update_models_list spróbuje pobrać modele
        print("Zakończono próbę aktualizacji listy modeli globalnych.")


        # --- POCZĄTEK DODANEJ LOGIKI SYNCHRONIZACJI ---
        print("Rozpoczęcie synchronizacji dostawcy z promptami...")
        new_global_provider = provider # Już mamy nowego dostawcę
        # Bezpośrednie wywołanie, update_models_list jest synchroniczne w kontekście GUI
        self._sync_provider_to_prompts(new_global_provider)
        # --- KONIEC DODANEJ LOGIKI SYNCHRONIZACJI ---

# Removed on_api_key_entry method as saving is now explicit via button
    # Removed on_api_key_entry method as saving is now explicit via button

    def on_global_model_change(self, *args):
        """Synchronizuje zmianę globalnego modelu ze wszystkimi promptami,
           które używają tego samego globalnego dostawcy."""
        new_global_model = self.model_name.get()
        global_provider = self.selected_provider.get()
        # Sprawdź, czy wybrany model nie jest pusty lub komunikatem o błędzie
        if new_global_model and not new_global_model.startswith("Błąd") and not new_global_model.startswith("Wprowadź klucz"):
            print(f"Synchronizacja globalnego modelu '{new_global_model}' (dostawca: {global_provider}) z promptami...")
            for i in range(NUM_SYSTEM_PROMPTS):
                # Ustaw model tylko jeśli dostawca promptu jest taki sam jak globalny
                if self.prompt_providers[i].get() == global_provider:
                    # Sprawdź, czy model globalny jest na liście dostępnych modeli dla tego promptu
                    prompt_model_list = self.prompt_model_combos[i].cget('values') if self.prompt_model_combos[i] else []
                    if new_global_model in prompt_model_list:
                        self.prompt_models[i].set(new_global_model)
                        print(f"  Prompt {i}: Ustawiono model na '{new_global_model}'.")
                        # Upewnij się, że combobox modelu promptu jest włączony
                        if self.prompt_model_combos[i]:
                             self.prompt_model_combos[i].configure(state="readonly")
                    else:
                        print(f"  Prompt {i}: Model globalny '{new_global_model}' nie znaleziony na liście modeli promptu, pozostawiono obecny model '{self.prompt_models[i].get()}'.")

                # else: # Opcjonalnie: logowanie pominięcia
                #     print(f"  Prompt {i}: Pominięto synchronizację modelu (dostawca promptu: {self.prompt_providers[i].get()}, globalny: {global_provider}).")
        else:
            print(f"Pominięto synchronizację globalnego modelu - model pusty lub błąd: '{new_global_model}'")


    # Dodana metoda pomocnicza do synchronizacji dostawcy z promptami
    def _sync_provider_to_prompts(self, new_global_provider):
        """Helper method to synchronize provider to all prompts and trigger their model list update."""
        print(f"Wywołano _sync_provider_to_prompts dla {new_global_provider}")

        for i in range(NUM_SYSTEM_PROMPTS):
            print(f"  Synchronizacja promptu {i}: Ustawianie dostawcy na '{new_global_provider}'")
            self.prompt_providers[i].set(new_global_provider)
            # Wywołaj on_prompt_provider_change, aby zaktualizować listę modeli dla tego promptu
            # Przekaż initial_load=False, bo to jest zmiana użytkownika
            print(f"  Wywołanie on_prompt_provider_change dla promptu {i}")
            self.on_prompt_provider_change(i, initial_load=False)
            # on_prompt_provider_change zajmie się ustawieniem domyślnego modelu/błędu dla nowego dostawcy.

        print("Zakończono synchronizację dostawcy z promptami.")

    def save_config(self):
        """Saves the current configuration to a JSON file."""
        # Ensure all key lists are padded before saving
        for provider in self.llm_providers:
             if provider != "Ollama (Local)":
                 self._ensure_api_keys_list(provider)

        # Get all system prompts
        system_prompts_to_save = []
        for i in range(NUM_SYSTEM_PROMPTS):
             text_area = getattr(self, f"prompt_text_{i}", None)
             if text_area: # CTkTextbox uses .get("0.0", "end")
                 system_prompts_to_save.append(text_area.get("0.0", "end").strip())
             else:
                 system_prompts_to_save.append("") # Append empty string if widget doesn't exist

        # Get per-prompt settings
        prompt_providers_to_save = [var.get() for var in self.prompt_providers]
        prompt_models_to_save = [var.get() for var in self.prompt_models]

        config_data = {
            # Restore global provider/model saving
            "selected_provider": self.selected_provider.get(),
            "model_name": self.model_name.get(),
            "api_keys": self.api_keys, # Save the dictionary of keys (remains global)
            "active_prompt_index": self.active_prompt_index.get(),
            "system_prompts": system_prompts_to_save, # Save list of 5 prompts
            "prompt_providers": prompt_providers_to_save, # Save per-prompt providers
            "prompt_models": prompt_models_to_save,       # Save per-prompt models
            "always_on_top": self.always_on_top_var.get() # Save the state of the checkbox
        }
        try:
            with open(CONFIG_FILE, "w", encoding="utf-8") as f:
                json.dump(config_data, f, indent=4)
            # Don't show messagebox on auto-save, maybe only on manual save?
            # messagebox.showinfo("Zapisano", "Konfiguracja została zapisana.")
            print("Konfiguracja zapisana.")
        except Exception as e:
            messagebox.showerror("Błąd Zapisu", f"Nie można zapisać konfiguracji: {e}")

    def load_config(self):
        """Loads configuration from the JSON file."""
        config_loaded = False
        default_prompts = [""] * NUM_SYSTEM_PROMPTS
        default_prompt_providers = [self.llm_providers[0]] * NUM_SYSTEM_PROMPTS # Default to first provider
        default_prompt_models = [""] * NUM_SYSTEM_PROMPTS

        if os.path.exists(CONFIG_FILE):
            try:
                with open(CONFIG_FILE, "r", encoding="utf-8") as f:
                    config_data = json.load(f)

                # Restore loading global provider/model
                loaded_provider = config_data.get("selected_provider", self.llm_providers[0])
                self.selected_provider.set(loaded_provider)
                self.model_name.set(config_data.get("model_name", "")) # Load last selected global model
                self.api_keys = config_data.get("api_keys", {}) # Load the dictionary of keys
                self.active_prompt_index.set(config_data.get("active_prompt_index", 0))
                # Load system prompts, ensuring correct length
                loaded_prompts = config_data.get("system_prompts", default_prompts)
                self.set_prompts_text(loaded_prompts) # Use the updated set_prompts_text
                self.always_on_top_var.set(config_data.get("always_on_top", False))
                # Wczytaj wymiary okna "Zawsze na wierzchu", używając domyślnych, jeśli brakuje
                self.on_top_window_width = config_data.get("on_top_window_width", 400)
                self.on_top_window_height = config_data.get("on_top_window_height", 600) # Użyj nowej domyślnej wysokości
                # Load per-prompt settings with defaults
                loaded_prompt_providers = config_data.get("prompt_providers", default_prompt_providers)
                loaded_prompt_models = config_data.get("prompt_models", default_prompt_models)

                # Ensure loaded lists have the correct length
                if len(loaded_prompt_providers) < NUM_SYSTEM_PROMPTS:
                    loaded_prompt_providers.extend(default_prompt_providers[len(loaded_prompt_providers):])
                elif len(loaded_prompt_providers) > NUM_SYSTEM_PROMPTS:
                    loaded_prompt_providers = loaded_prompt_providers[:NUM_SYSTEM_PROMPTS]

                if len(loaded_prompt_models) < NUM_SYSTEM_PROMPTS:
                    loaded_prompt_models.extend(default_prompt_models[len(loaded_prompt_models):])
                elif len(loaded_prompt_models) > NUM_SYSTEM_PROMPTS:
                    loaded_prompt_models = loaded_prompt_models[:NUM_SYSTEM_PROMPTS]

                # Set the loaded values, falling back to global if empty/default
                global_provider = self.selected_provider.get()
                global_model = self.model_name.get()
                default_provider = self.llm_providers[0] # Default provider used for comparison

                for i in range(NUM_SYSTEM_PROMPTS):
                    # Set provider, fallback to global if loaded is empty or default
                    p_provider = loaded_prompt_providers[i]
                    if not p_provider or p_provider == default_provider:
                        self.prompt_providers[i].set(global_provider)
                    else:
                        self.prompt_providers[i].set(p_provider)

                    # Set model, fallback to global if loaded is empty
                    p_model = loaded_prompt_models[i]
                    if not p_model:
                        self.prompt_models[i].set(global_model)
                    else:
                        self.prompt_models[i].set(p_model)

                # Ensure loaded keys lists have the correct length and set UI (based on global provider)
                for provider in self.llm_providers:
                     if provider != "Ollama (Local)":
                         self._ensure_api_keys_list(provider) # Pad/truncate loaded lists

                # Set the API key UI based on the loaded GLOBAL provider
                self.api_key_selector_combo.set("Klucz 1") # Default to first key view
                self.selected_api_key_index.set(0)
                # Use the loaded global provider here
                keys_for_loaded_provider = self._ensure_api_keys_list(loaded_provider)
                self.current_api_key.set(keys_for_loaded_provider[0] if keys_for_loaded_provider else "") # Display the first key

                self.update_api_key_visibility() # Ensure correct visibility based on the loaded global provider

                # Trigger GLOBAL model list update first
                self.update_models_list() # Attempt to load models for the loaded global provider

                # Then trigger model list update for each loaded per-prompt setting
                for i in range(NUM_SYSTEM_PROMPTS):
                    self.on_prompt_provider_change(i, initial_load=True)
                config_loaded = True
                self.update_prompt_highlight() # Upewnienie się, że podświetlenie jest wywoływane po załadowaniu

            except Exception as e:
                messagebox.showerror("Błąd Odczytu", f"Nie można odczytać konfiguracji: {e}")
                # Reset API keys and try fetching models if config load fails
                self.api_keys = {}
        # This block should be at the same indentation level as the try/except above it
        if not config_loaded:
             # Set default prompts text
             self.set_prompts_text(default_prompts)
             # Set default per-prompt settings based on global defaults
             global_provider = self.selected_provider.get() # Should be default provider here
             global_model = self.model_name.get() # Should be empty here
             for i in range(NUM_SYSTEM_PROMPTS):
                 self.prompt_providers[i].set(global_provider)
                 self.prompt_models[i].set(global_model) # Set to empty initially

             # Ensure default empty API keys lists exist
             for provider_name in self.llm_providers:
                 if provider_name != "Ollama (Local)":
                     self._ensure_api_keys_list(provider_name)

             # Trigger default global state update (which loads global models)
             self.on_provider_change()
             # Then trigger per-prompt updates (which will likely show errors initially if key needed)
             for i in range(NUM_SYSTEM_PROMPTS):
                 self.on_prompt_provider_change(i, initial_load=True)

        # Ensure the on-top window state matches the loaded config
        self.toggle_on_top_window()


    def set_active_prompt(self, index):
        """Sets the clicked prompt text area as active and updates highlighting."""
        if 0 <= index < NUM_SYSTEM_PROMPTS:
            current_index = self.active_prompt_index.get()
            if current_index != index: # Zaktualizuj tylko jeśli indeks się zmienił
                self.active_prompt_index.set(index)
                self.update_prompt_highlight()
                print(f"Aktywowano prompt {index + 1}") # Debugging

    def update_prompt_highlight(self):
        """Updates the visual highlight for the active prompt text area."""
        active_index = self.active_prompt_index.get()
        # Define colors (można dostosować)
        try:
            # Próba uzyskania kolorów z motywu
            active_border_color = customtkinter.ThemeManager.theme["CTkButton"]["fg_color"]
            default_border_color = customtkinter.ThemeManager.theme["CTkTextbox"]["border_color"]
            active_border_width = 2
            default_border_width = customtkinter.ThemeManager.theme["CTkTextbox"]["border_width"]
        except KeyError:
            # Domyślne kolory, jeśli motyw nie jest w pełni załadowany lub klucze nie istnieją
            print("[WARN] Nie można pobrać kolorów z motywu, używanie domyślnych.")
            active_border_color = "blue" # Lub inny bezpieczny kolor
            default_border_color = "gray" # Lub inny bezpieczny kolor
            active_border_width = 2
            default_border_width = 1 # Zazwyczaj 1 lub 0

        for i in range(NUM_SYSTEM_PROMPTS):
            text_area = getattr(self, f"prompt_text_{i}", None)
            if text_area:
                if i == active_index:
                    # Zastosuj podświetlenie do aktywnego pola
                    text_area.configure(border_color=active_border_color, border_width=active_border_width)
                else:
                    # Przywróć domyślny wygląd pozostałym
                    text_area.configure(border_color=default_border_color, border_width=default_border_width)

    def toggle_monitoring(self):
        """Starts or stops the clipboard monitoring thread."""
        if self.is_monitoring:
            self.is_monitoring = False
            self.start_stop_button.configure(text="Start Monitorowania") # CTk uses configure
            if self.monitoring_thread:
                # No direct way to stop a thread, relies on the loop condition
                print("Zatrzymywanie monitorowania...")
        else:
            # Validate configuration before starting
            # Validate global settings before starting
            provider = self.selected_provider.get()
            model = self.model_name.get()
            api_key = self.get_selected_api_key() # Global API key check

            if provider not in ["Ollama (Local)", "Anthropic"] and not api_key: # Check if key is needed and missing
                 messagebox.showwarning("Brak Klucza API", f"Proszę wybrać i/lub wprowadzić klucz API (Slot {self.selected_api_key_index.get()+1}) dla {provider} w zakładce Konfiguracja.")
                 return
            # Update error messages check
            # Use defined constants for error messages
            error_messages = [ERROR_NO_API_KEY_TEMPLATE.format(slot=i+1) for i in range(NUM_API_KEYS)] + \
                             [ERROR_FETCHING, ERROR_OLLAMA_CONNECTION, NO_MODELS_FOUND, ERROR_GUI, "Pobieranie..."]
            if not model or model in error_messages:
                 messagebox.showwarning("Brak Nazwy Modelu", "Proszę wybrać poprawny model z listy.")
                 return

            self.is_monitoring = True
            self.start_stop_button.configure(text="Stop Monitorowania") # CTk uses configure
            self.last_clipboard_content = pyperclip.paste() # Initialize last content
            print("Rozpoczynanie monitorowania...")
            self.monitoring_thread = threading.Thread(target=self.monitor_clipboard, daemon=True)
            self.monitoring_thread.start()

    def monitor_clipboard(self):
        """Continuously monitors the clipboard for changes."""
        while self.is_monitoring:
            # Usunięto zbędny blok try
            text_changed = False
            image_changed = False
            current_text_content = ""
            current_image_content = None
            current_image_hash = None

            try:
                # 1. Sprawdź zawartość tekstową
                current_text_content = pyperclip.paste()
                if current_text_content != self.last_clipboard_content:
                    print("Wykryto zmianę w schowku (tekst).")
                    self.last_clipboard_content = current_text_content
                    text_changed = True

                # 2. Sprawdź zawartość obrazu
                try:
                    time.sleep(0.1) # Dodano małe opóźnienie przed odczytem obrazu
                    current_image_content = ImageGrab.grabclipboard()
                    # --- POCZĄTEK LOGOWANIA DEBUGOWANIA ---
                    if isinstance(current_image_content, Image.Image):
                        print(f"[DEBUG monitor_clipboard] ImageGrab.grabclipboard() zwrócił obraz: Mode={current_image_content.mode}, Size={current_image_content.size}")
                        try:
                            # Zapisz obraz do pliku dla weryfikacji
                            # Użyj os.path.dirname(__file__) dla bezpieczeństwa, jeśli skrypt jest uruchamiany z innego miejsca
                            script_dir = os.path.dirname(os.path.abspath(__file__))
                            debug_image_path = os.path.join(script_dir, "debug_clipboard_image.png")
                            current_image_content.save(debug_image_path, format='PNG')
                            print(f"[DEBUG monitor_clipboard] Zapisano pobrany obraz do: {debug_image_path}")
                        except Exception as save_err:
                            print(f"[DEBUG monitor_clipboard] Błąd zapisu obrazu debugowania: {save_err}")
                    elif current_image_content is not None:
                         print(f"[DEBUG monitor_clipboard] ImageGrab.grabclipboard() zwrócił coś, co nie jest obrazem PIL: {type(current_image_content)}")
                    else:
                         print("[DEBUG monitor_clipboard] ImageGrab.grabclipboard() zwrócił None (brak obrazu).")
                    # --- KONIEC LOGOWANIA DEBUGOWANIA ---

                    # Uproszczona logika: Jeśli jest obraz, traktuj jako zmianę.
                    if isinstance(current_image_content, Image.Image):
                        print("[DEBUG monitor_clipboard] Wykryto obraz w schowku. Ustawianie image_changed=True.")
                        image_changed = True
                        # Nie potrzebujemy już hashowania ani last_clipboard_image_hash w tej metodzie
                    else:
                        # Jeśli nie ma obrazu, upewnij się, że image_changed jest False
                        # (chociaż powinno już być False z inicjalizacji na początku pętli)
                        image_changed = False
                        current_image_content = None # Upewnij się, że jest None

                except Exception as img_e:
                    # Ignoruj błędy podczas pobierania obrazu, jeśli obraz nie jest obecny
                    # print(f"Błąd podczas sprawdzania obrazu w schowku: {img_e}")
                    if self.last_clipboard_image_hash is not None:
                         print("[DEBUG monitor_clipboard] Obraz usunięty ze schowka (błąd ImageGrab). Resetowanie hasha.")
                         self.last_clipboard_image_hash = None
                         image_changed = True # Traktuj błąd jako usunięcie, jeśli wcześniej mieliśmy obraz
                         print(f"[DEBUG monitor_clipboard] Ustawiono image_changed={image_changed} po błędzie ImageGrab.")
                    current_image_content = None

                # 3. Przetwarzaj, jeśli tekst lub obraz się zmienił
                # --- POCZĄTEK LOGOWANIA DEBUGOWANIA ---
                print(f"[DEBUG monitor_clipboard] Sprawdzanie warunku przetwarzania: text_changed={text_changed}, image_changed={image_changed}")
                # --- KONIEC LOGOWANIA DEBUGOWANIA ---
                if text_changed or image_changed:
                    # Sprawdź czy zawartość tekstowa zawiera triggery agentów
                    text_to_check = self.last_clipboard_content.strip()
                    agent_trigger_detected = False

                    if text_to_check.startswith("@agent "):
                        print("[DEBUG monitor_clipboard] Wykryto trigger @agent")
                        agent_trigger_detected = True
                        # Usuń trigger z zawartości
                        content_without_trigger = text_to_check[7:].strip()  # Usuń "@agent "
                        image_to_process = current_image_content if image_changed and current_image_content else None
                        self.root.after(0, self.process_agent_request, content_without_trigger, image_to_process, "single")
                    elif text_to_check.startswith("@super "):
                        print("[DEBUG monitor_clipboard] Wykryto trigger @super")
                        agent_trigger_detected = True
                        # Usuń trigger z zawartości
                        content_without_trigger = text_to_check[7:].strip()  # Usuń "@super "
                        image_to_process = current_image_content if image_changed and current_image_content else None
                        self.root.after(0, self.process_agent_request, content_without_trigger, image_to_process, "multi")

                    # Jeśli nie wykryto triggerów agentów, przetwarzaj normalnie
                    if not agent_trigger_detected:
                        # Zawsze używaj najnowszej zawartości tekstowej, przekaż obraz tylko wtedy, gdy się zmienił *i* istnieje
                        image_to_process = current_image_content if image_changed and current_image_content else None
                        # --- POCZĄTEK LOGOWANIA DEBUGOWANIA ---
                        print(f"[DEBUG monitor_clipboard] Wywoływanie process_clipboard_content:")
                        print(f"  - text_content: '{self.last_clipboard_content[:50]}...'")
                        print(f"  - image_to_process: {type(image_to_process)}")
                        if isinstance(image_to_process, Image.Image):
                            print(f"  - image_to_process details: Mode={image_to_process.mode}, Size={image_to_process.size}")
                        else:
                            print(f"  - image_to_process jest None lub innego typu.")
                        # --- KONIEC LOGOWANIA DEBUGOWANIA ---
                        # Przetwarzaj w głównym wątku za pomocą 'after', aby uniknąć problemów z GUI z wątku w tle
                        self.root.after(0, self.process_clipboard_content, self.last_clipboard_content, image_to_process)

                    # Odtwórz dźwięk powiadomienia
                    if platform.system() == "Windows":
                        try:
                            winsound.PlaySound("SystemAsterisk", winsound.SND_ALIAS | winsound.SND_ASYNC)
                        except Exception as e:
                            print(f"Nie można odtworzyć dźwięku: {e}")

            except pyperclip.PyperclipException as e:
                print(f"Błąd odczytu schowka (pyperclip): {e}")
                # Jeśli odczyt tekstu nie powiódł się, nadal sprawdzaj zmianę obrazu
                if image_changed:
                     # Użyj image_to_process, który został już określony
                     image_arg = current_image_content if current_image_content else None
                     print("[DEBUG monitor_clipboard] Błąd pyperclip, ale wykryto zmianę obrazu. Wywoływanie process_clipboard_content tylko z obrazem.")
                     self.root.after(0, self.process_clipboard_content, self.last_clipboard_content, image_arg) # Przekaż ostatni tekst i obraz
            except Exception as e:
                print(f"Nieoczekiwany błąd w pętli monitorowania: {e}")

            time.sleep(1) # Sprawdzaj co sekundę

    def is_multimodal(self, provider, model_name):
        """Sprawdza, czy dany model obsługuje wejście obrazu."""
        if not model_name:
            return False
        model_lower = model_name.lower()
        if provider == "OpenAI":
            # gpt-4-turbo obsługuje również wizję od kwietnia 2024
            return "vision" in model_lower or model_lower.startswith("gpt-4-turbo") or model_lower.startswith("gpt-4o")
        elif provider == "Google Gemini":
            # Sprawdź znane modele wizyjne i ogólnie modele zaczynające się od "gemini"
            # ponieważ nowsze/eksperymentalne modele mogą obsługiwać wizję bez "vision" w nazwie.
            # Wyklucz jawnie znane modele tylko tekstowe, jeśli to konieczne.
            # Poprawiono warunek, aby obejmował szerszy zakres modeli Gemini
            return "vision" in model_lower or model_lower.startswith("gemini")
        elif provider == "Anthropic":
            # Modele Claude 3 obsługują wizję
            return model_lower.startswith("claude-3")
        elif provider == "Ollama (Local)":
            # Popularne modele multimodalne dla Ollama
            return model_lower.startswith("llava") or model_lower.startswith("bakllava") # Dodaj inne znane modele wizyjne Ollama w razie potrzeby
        elif provider == "OpenRouter":
            # OpenRouter używa różnych modeli, sprawdź popularne wskaźniki wizji
            # Może to wymagać dopracowania w oparciu o specyficzne konwencje nazewnictwa modeli OpenRouter
            return "vision" in model_lower or model_lower.startswith("gpt-4-turbo") or model_lower.startswith("gpt-4o") or model_lower.startswith("claude-3") or model_lower.startswith("gemini")
        elif provider == "DeepSeek":
            # DeepSeek obecnie nie obsługuje wizji w standardowych modelach
            return False
        return False

    def prepare_image_for_api(self, image_content: Image.Image, max_size_kb=4096, target_format='PNG'): # Zwiększony domyślny maksymalny rozmiar
        """
        Konwertuje obraz PIL na base64, zmieniając rozmiar w razie potrzeby.
        Zwraca krotkę: (string_base64, typ_mediów) lub (None, None) w przypadku błędu.
        """
        if not isinstance(image_content, Image.Image):
            return None, None

        try:
            # Upewnij się, że obraz jest w trybie RGB dla spójnego przetwarzania (ważne przy zapisie JPEG)
            if image_content.mode != 'RGB':
                 print(f"Konwersja obrazu z trybu {image_content.mode} do RGB.")
                 image_content = image_content.convert('RGB')

            # Użyj JPEG dla potencjalnie lepszej kompresji, w razie potrzeby cofnij się do PNG
            if target_format.upper() not in ['JPEG', 'PNG', 'WEBP']:
                 print(f"Nieobsługiwany format docelowy '{target_format}', używanie PNG.")
                 target_format = 'PNG'

            output_buffer = io.BytesIO()
            if target_format.upper() == 'JPEG':
                 # Użyj ustawienia jakości dla JPEG
                 image_content.save(output_buffer, format='JPEG', quality=85) # Dostosuj jakość w razie potrzeby
                 media_type = "image/jpeg"
            else: # PNG lub WEBP (Pillow może wymagać zainstalowanego wsparcia dla webp)
                 image_content.save(output_buffer, format=target_format)
                 media_type = f"image/{target_format.lower()}"

            size_kb = len(output_buffer.getvalue()) / 1024
            print(f"Rozmiar początkowy obrazu ({target_format}): {size_kb:.2f} KB")

            # Zmień rozmiar, jeśli jest za duży
            while size_kb > max_size_kb:
                print(f"Obraz za duży ({size_kb:.2f} KB > {max_size_kb} KB), zmniejszanie rozmiaru...")
                width, height = image_content.size
                scale_factor = (max_size_kb / size_kb) ** 0.5 # Oszacuj potrzebny współczynnik skali
                new_width = int(width * scale_factor * 0.95) # Zmniejsz nieco bardziej na wszelki wypadek
                new_height = int(height * scale_factor * 0.95)

                if new_width < 50 or new_height < 50: # Zapobiegaj zmianie rozmiaru na zbyt mały
                     print("Osiągnięto minimalny rozmiar obrazu, nie można dalej zmniejszać.")
                     # Spróbuj zapisać z niższą jakością, jeśli JPEG, przed poddaniem się
                     if target_format.upper() == 'JPEG':
                          print("Próba zapisu JPEG z niższą jakością (70)...")
                          output_buffer = io.BytesIO()
                          image_content.save(output_buffer, format='JPEG', quality=70)
                          size_kb = len(output_buffer.getvalue()) / 1024
                          if size_kb <= max_size_kb:
                               print(f"Zapisano z niższą jakością, rozmiar: {size_kb:.2f} KB")
                               break # Sukces z niższą jakością
                     return None, None # Wskaż niepowodzenie

                image_content = image_content.resize((new_width, new_height), Image.Resampling.LANCZOS)

                # Przelicz rozmiar z wybranym formatem
                output_buffer = io.BytesIO()
                if target_format.upper() == 'JPEG':
                     image_content.save(output_buffer, format='JPEG', quality=85)
                else:
                     image_content.save(output_buffer, format=target_format)
                size_kb = len(output_buffer.getvalue()) / 1024
                print(f"Nowy rozmiar obrazu ({target_format}): {size_kb:.2f} KB")


            # Zakoduj końcowy obraz do base64
            base64_encoded_image = base64.b64encode(output_buffer.getvalue()).decode('utf-8')
            print(f"Obraz przygotowany (format: {media_type}, rozmiar: {size_kb:.2f} KB)")
            return base64_encoded_image, media_type

        except Exception as e:
            print(f"Błąd podczas przygotowywania obrazu: {e}")
            return None, None
        print("Monitorowanie zakończone.")


    def process_clipboard_content(self, text_content, image_content=None):
        """Wysyła tekst i/lub zawartość obrazu ze schowka do wybranego LLM."""
        # --- DEBUG LOGGING START ---
        print(f"[DEBUG process_clipboard_content] Otrzymano:")
        print(f"  - text_content: '{text_content[:50]}...'")
        print(f"  - image_content: {type(image_content)}")
        if isinstance(image_content, Image.Image):
            print(f"  - image_content details: Mode={image_content.mode}, Size={image_content.size}")
        # --- DEBUG LOGGING END ---
        active_index = self.active_prompt_index.get()
        prompt_provider = self.prompt_providers[active_index].get()
        prompt_model = self.prompt_models[active_index].get()

        non_usable_prompt_models = ["", "Pobieranie...", ERROR_GUI, NO_MODELS_FOUND, ERROR_FETCHING, ERROR_OLLAMA_CONNECTION, "Brak klucza globalnego"]
        non_usable_prompt_models.extend([ERROR_NO_API_KEY_TEMPLATE.format(slot=i+1) for i in range(NUM_API_KEYS)])

        if prompt_model not in non_usable_prompt_models:
            provider = prompt_provider
            model = prompt_model
            using_prompt_settings = True
        else:
            provider = self.selected_provider.get()
            model = self.model_name.get()
            using_prompt_settings = False
            print(f"[DEBUG] Używanie ustawień globalnych (Prompt {active_index + 1} miał model: '{prompt_model}')")

        print(f"Przetwarzanie dla promptu {active_index + 1} (Ustawienia {'promptu' if using_prompt_settings else 'globalne'}): Dostawca={provider}, Model={model}")

        global_error_messages = [ERROR_NO_API_KEY_TEMPLATE.format(slot=i+1) for i in range(NUM_API_KEYS)] + \
                                [ERROR_FETCHING, ERROR_OLLAMA_CONNECTION, NO_MODELS_FOUND, ERROR_GUI, "Pobieranie...", "Brak modeli"]

        if not model or model in global_error_messages:
            error_msg = f"Błąd: Model '{model}' nieprawidłowy lub wymaga konfiguracji."
            print(f"[ERROR] {error_msg} Przetwarzanie zatrzymane.")
            self._update_on_top_widget(error_msg)
            return

        system_prompt = self.get_active_prompt()
        # Zmieniono nazwę zmiennej z 'content' na 'text_content'
        print(f"Tekst wejściowy: {text_content[:100] if text_content else 'Brak'}...")

        prepared_image_data = None
        image_media_type = None
        is_vision_model = self.is_multimodal(provider, model)

        # --- DEBUG LOGGING START ---
        print(f"[DEBUG process_clipboard_content] Sprawdzanie warunku przetwarzania obrazu:")
        print(f"  - image_content jest obiektem Image? {isinstance(image_content, Image.Image)}")
        print(f"  - is_vision_model? {is_vision_model}")
        # --- DEBUG LOGGING END ---
        if image_content and is_vision_model:
            print("Wykryto obraz dla modelu multimodalnego, przygotowywanie...")
            # Preferuj JPEG dla mniejszego rozmiaru, cofnij się do PNG
            prepared_image_data, image_media_type = self.prepare_image_for_api(image_content, target_format='JPEG')
            if not prepared_image_data:
                 print("[WARN] Nie udało się przygotować obrazu jako JPEG, próba PNG...")
                 prepared_image_data, image_media_type = self.prepare_image_for_api(image_content, target_format='PNG')

            if not prepared_image_data:
                 print("[WARN] Nie udało się przygotować obrazu, wysyłanie tylko tekstu.")
                 self._update_on_top_widget("Ostrzeżenie: Nie udało się przetworzyć obrazu.")
                 # Kontynuuj bez obrazu
            else:
                 print(f"Obraz przygotowany jako {image_media_type}.")
                 # Uwaga: API Gemini może preferować surowy obiekt PIL, dostosuj call_gemini w razie potrzeby

        elif image_content and not is_vision_model:
            print(f"[INFO] Obraz w schowku, ale model '{model}' nie jest multimodalny. Obraz zostanie zignorowany.")
            self._update_on_top_widget(f"Info: Obraz zignorowany (model '{model}' nie obsługuje obrazów).")
            image_content = None # Upewnij się, że obraz nie jest przekazywany dalej


        self._update_on_top_widget("Przetwarzanie...")
        response = ""
        try:
            start_time = time.time()
            # --- Wywołaj odpowiedniego dostawcę LLM ---
            # Przekaż prepared_image_data (string base64) i image_media_type, jeśli dostępne
            # Uwaga: call_gemini przyjmuje obiekt PIL (image_content)
            if provider == "OpenAI":
                response = self.call_openai(model, system_prompt, text_content, prepared_image_data, image_media_type)
            elif provider == "Anthropic":
                 response = self.call_anthropic(model, system_prompt, text_content, prepared_image_data, image_media_type)
            elif provider == "Google Gemini":
                 # Przekaż oryginalny obiekt obrazu PIL (image_content), jeśli istnieje i model jest multimodalny
                 gemini_image_input = image_content if image_content and is_vision_model else None
                 response = self.call_gemini(model, system_prompt, text_content, image_pil=gemini_image_input)
            elif provider == "Ollama (Local)":
                 # Przekaż string base64, jeśli dostępny
                 ollama_image_base64 = prepared_image_data if isinstance(prepared_image_data, str) else None
                 response = self.call_ollama(model, system_prompt, text_content, ollama_image_base64) # Przekaż string base64
            elif provider == "OpenRouter":
                 # OpenRouter używa prostego formatu prompt (bez obsługi obrazów)
                 full_prompt = f"{system_prompt}\n\n{text_content}" if system_prompt else text_content
                 response = self.call_openrouter(model, full_prompt)
            elif provider == "DeepSeek":
                 # DeepSeek używa prostego formatu prompt (bez obsługi obrazów)
                 full_prompt = f"{system_prompt}\n\n{text_content}" if system_prompt else text_content
                 response = self.call_deepseek(model, full_prompt)
            else:
                messagebox.showerror("Błąd", f"Nieznany dostawca LLM: {provider}")
                self._update_on_top_widget(f"Błąd: Nieznany dostawca LLM: {provider}")
                return

            end_time = time.time()
            print(f"Czas odpowiedzi LLM: {end_time - start_time:.2f}s")

            if response:
                print(f"Odpowiedź LLM: {response[:100]}...")
                try:
                    pyperclip.copy(response)
                    print("Odpowiedź skopiowana do schowka.")
                    self._update_on_top_widget(response)
                    # Zaktualizuj last_clipboard_content TYLKO jeśli odpowiedź jest tekstem (zapobiegaj pętli obrazu)
                    self.last_clipboard_content = response
                    # Zresetuj hash obrazu, ponieważ odpowiedź jest tekstem, a nie obrazem
                    self.last_clipboard_image_hash = None
                except pyperclip.PyperclipException as e:
                    messagebox.showerror("Błąd Schowka", f"Nie można skopiować odpowiedzi do schowka: {e}")
                    self._update_on_top_widget(f"Błąd kopiowania odpowiedzi: {e}")
            else:
                print("Otrzymano pustą odpowiedź od LLM.")
                self._update_on_top_widget("Otrzymano pustą odpowiedź.")


        except openai.AuthenticationError:
             error_msg = f"Błąd OpenAI: Problem z kluczem API (Slot {self.selected_api_key_index.get()+1}). Sprawdź klucz."
             print(error_msg)
             messagebox.showerror("Błąd OpenAI", error_msg)
             self._update_on_top_widget(error_msg)
        except anthropic.AuthenticationError:
             error_msg = f"Błąd Anthropic: Problem z kluczem API (Slot {self.selected_api_key_index.get()+1}). Sprawdź klucz."
             print(error_msg)
             messagebox.showerror("Błąd Anthropic", error_msg)
             self._update_on_top_widget(error_msg)
        except Exception as e:
            error_msg = f"Błąd podczas wywołania LLM ({provider}): {e}"
            print(error_msg)
            # Sprawdź, czy błąd jest specyficznie związany z kluczem API Gemini
            if provider == "Google Gemini" and ("API key not valid" in str(e) or "User location is not supported" in str(e)):
                 error_msg = f"Błąd Google Gemini (Slot {self.selected_api_key_index.get()+1}): {e}"
                 messagebox.showerror(f"Błąd - {provider}", error_msg)
            else:
                 messagebox.showerror(f"Błąd - {provider}", error_msg)
            self._update_on_top_widget(f"Błąd: {e}")

    def _update_on_top_widget(self, text):
        """Helper function to update the on-top text widget (called via root.after)."""
        # Note: CTkTextbox state handling might differ or not be needed if always editable
        if self.on_top_window and self.on_top_window.winfo_exists() and self.on_top_text_widget:
            try:
                self.on_top_text_widget.configure(state="normal") # Enable editing
                self.on_top_text_widget.delete("1.0", tk.END)
                self.on_top_text_widget.insert("1.0", text)
                self.on_top_text_widget.config(state=tk.DISABLED)
                self.on_top_text_widget.see(tk.END) # Scroll to the end
            except tk.TclError as e:
                 print(f"Błąd aktualizacji widgetu 'Zawsze na wierzchu': {e}")
                 self.on_top_window = None
                 self.on_top_text_widget = None
                 self.always_on_top_var.set(False)


    # --- Model Fetching ---

    def fetch_models_for_provider(self, provider): # Accept provider as argument
        """Fetches the list of available models for the given provider."""
        # Define specific error messages
        error_key_msg = f"Wprowadź klucz API (Slot {self.selected_api_key_index.get()+1})"
        ERROR_NO_API_KEY = error_key_msg
        ERROR_FETCHING = "Błąd pobierania modeli"
        ERROR_CONNECTION = "Błąd połączenia z Ollama"
        NO_MODELS_FOUND = "Brak modeli"

        # provider = self.selected_provider.get() # Use the passed argument instead
        api_key = self.get_selected_api_key() # Use the helper method
        model_list = []

        # DEBUG: Logowanie dostawcy i klucza API
        api_key_display = f"{api_key[:4]}...{api_key[-4:]}" if api_key and len(api_key) > 8 else api_key
        print(f"[DEBUG] Pobieranie modeli dla: {provider}, Klucz Slot: {self.selected_api_key_index.get()+1}, Klucz API: {api_key_display}")


        try:
            if provider == "OpenAI":
                if not api_key: return [ERROR_NO_API_KEY]
                print(f"[DEBUG] OpenAI: Próba inicjalizacji klienta...")
                client = openai.OpenAI(api_key=api_key)
                models = client.models.list()
                print(f"[DEBUG] OpenAI: Otrzymano {len(models.data)} modeli. Filtrowanie...")
                model_list = sorted([model.id for model in models.data if "gpt" in model.id]) # Filter for GPT models for relevance
                print(f"[DEBUG] OpenAI: Przefiltrowano do {len(model_list)} modeli.")
            elif provider == "Anthropic":
                # No direct API endpoint found, using a hardcoded list
                model_list = [
                    "claude-3-5-sonnet-20241022",
                    "claude-3-opus-20240229",
                    "claude-3-sonnet-20240229",
                    "claude-3-haiku-20240307",
                    # Add older models if needed
                    "claude-2.1",
                    "claude-2.0",
                    "claude-instant-1.2"
                ]
            elif provider == "Google Gemini":
                if not api_key: return [ERROR_NO_API_KEY]
                print(f"[DEBUG] Gemini: Próba konfiguracji klienta...")
                genai.configure(api_key=api_key)
                models = genai.list_models()
                # Filter for models supporting 'generateContent'
                print(f"[DEBUG] Gemini: Otrzymano listę modeli. Filtrowanie...") # Gemini zwraca iterator, nie listę od razu
                raw_model_list = list(models) # Konwersja na listę, aby policzyć
                print(f"[DEBUG] Gemini: Otrzymano {len(raw_model_list)} modeli. Filtrowanie...")
                model_list = sorted([m.name.split('/')[-1] for m in raw_model_list if 'generateContent' in m.supported_generation_methods])
                print(f"[DEBUG] Gemini: Przefiltrowano do {len(model_list)} modeli.")
            elif provider == "Ollama (Local)":
                try:
                    # Use the configured Ollama endpoint if available, otherwise default
                    ollama_endpoint = self.get_ollama_endpoint() # Helper function to get endpoint
                    tags_url = f"{ollama_endpoint.rstrip('/')}/api/tags"
                    print(f"[DEBUG] Ollama: Próba połączenia z {tags_url}...")
                    response = requests.get(tags_url, timeout=5)
                    response.raise_for_status()
                    models_data = response.json()
                    print(f"[DEBUG] Ollama: Otrzymano odpowiedź. Przetwarzanie...")
                    model_list = sorted([model['name'] for model in models_data.get('models', [])])
                    print(f"[DEBUG] Ollama: Znaleziono {len(model_list)} modeli.")
                except requests.exceptions.ConnectionError:
                    return [ERROR_CONNECTION]
                except Exception as e:
                    print(f"Błąd pobierania modeli Ollama: {e}")
                    return [ERROR_FETCHING]
            elif provider == "OpenRouter":
                 if not api_key: return [ERROR_NO_API_KEY]
                 print(f"[DEBUG] OpenRouter: Próba inicjalizacji klienta...")
                 # Use OpenAI client with OpenRouter base URL
                 client = openai.OpenAI(
                     base_url="https://openrouter.ai/api/v1",
                     api_key=api_key,
                 )
                 models = client.models.list()
                 print(f"[DEBUG] OpenRouter: Otrzymano {len(models.data)} modeli.")
                 model_list = sorted([model.id for model in models.data]) # Get all models from OpenRouter
            elif provider == "DeepSeek":
                 if not api_key: return [ERROR_NO_API_KEY]
                 print(f"[DEBUG] DeepSeek: Próba inicjalizacji klienta...")
                 # Use OpenAI client with DeepSeek base URL
                 client = openai.OpenAI(
                     base_url="https://api.deepseek.com",
                     api_key=api_key,
                 )
                 models = client.models.list()
                 print(f"[DEBUG] DeepSeek: Otrzymano {len(models.data)} modeli.")
                 model_list = sorted([model.id for model in models.data]) # Get all models from DeepSeek

        except openai.AuthenticationError as e:
             print(f"Błąd autentykacji dla {provider} (Slot {self.selected_api_key_index.get()+1}): {e}")
             # messagebox.showerror("Błąd Klucza API", f"Nieprawidłowy klucz API dla {provider} (Slot {self.selected_api_key_index.get()+1}). Sprawdź klucz.") # Don't show popup on fetch error
             return [ERROR_NO_API_KEY] # Return specific error
        except Exception as e:
            print(f"Błąd podczas pobierania modeli dla {provider}: {e}")
            # messagebox.showerror("Błąd Pobierania Modeli", f"Nie można pobrać listy modeli dla {provider}: {e}") # Don't show popup on fetch error
            return [ERROR_FETCHING] # Indicate error in the list

        print(f"Znaleziono modeli dla {provider}: {len(model_list)}")
        return model_list if model_list else [NO_MODELS_FOUND]

    def update_models_list(self, *args):
        """Updates the model combobox with models for the selected provider."""
        # This function now assumes the API key check (if needed) was done by the caller
        # (on_provider_change or save_selected_api_key)

        # Disable combobox during update
        self.model_combobox.configure(state="disabled") # Use string "disabled" for CTk state
        self.model_combobox['values'] = ["Pobieranie..."]
        self.model_name.set("Pobieranie...") # Set display value during fetch
        self.root.update_idletasks() # Force GUI update to show "Pobieranie..."

        # Run fetching in a separate thread to avoid blocking GUI
        threading.Thread(target=self._fetch_and_update_models, daemon=True).start()


    def _fetch_and_update_models(self):
        """Helper function to fetch models and update GUI from a thread."""
        models = self.fetch_models_for_provider()

        # Schedule GUI update back on the main thread
        self.root.after(0, self._update_combobox_values, models)

    # --- Per-Prompt Model Update Logic ---

    def on_prompt_provider_change(self, index, initial_load=False):
        """Called when the provider for a specific prompt slot changes."""
        provider = self.prompt_providers[index].get()
        model_combo = self.prompt_model_combos[index]
        model_var = self.prompt_models[index]

        if not model_combo: # Should not happen if create_widgets ran correctly
            print(f"[ERROR] Model combobox for index {index} not found.")
            return

        print(f"[DEBUG] Provider changed for prompt {index+1} to: {provider}")

        # Clear and disable model combobox
        model_var.set("Pobieranie...")
        model_combo.configure(values=["Pobieranie..."], state="disabled")
        self.root.update_idletasks() # Ensure "Pobieranie..." is shown

        # Remove API key check here - fetch_models_for_provider will handle it
        # and return the specific error message if the key is missing/invalid.

        # Fetch models in a separate thread
        threading.Thread(target=self._fetch_and_update_prompt_models, args=(index, provider), daemon=True).start()

    def _fetch_and_update_prompt_models(self, index, provider):
        """Helper function to fetch models for a specific prompt slot."""
        print(f"[DEBUG] Fetching models for prompt {index+1} (Provider: {provider})")
        # Note: fetch_models_for_provider uses the *global* API key selector right now.
        # This might need adjustment if API keys become per-prompt in the future.
        models = self.fetch_models_for_provider(provider) # Pass provider explicitly
        self.root.after(0, self._update_prompt_combobox_values, index, models)

    def _update_prompt_combobox_values(self, index, models):
        """Updates the model combobox values for a specific prompt slot. Simplified logic."""
        model_combo = self.prompt_model_combos[index]
        model_var = self.prompt_models[index]

        if not model_combo:
            print(f"[ERROR] Model combobox for index {index} not found during update.")
            return

        try:
            last_selected_model = model_var.get() # Get current value before potential changes
            model_to_set = ""
            state_to_set = "disabled" # Default to disabled

            # Define fetch error messages (excluding API key error)
            fetch_error_messages = [ERROR_FETCHING, ERROR_OLLAMA_CONNECTION, NO_MODELS_FOUND, ERROR_GUI, "Pobieranie..."]
            # Define the specific API key error message that fetch_models_for_provider returns
            api_key_error_msg = ERROR_NO_API_KEY_TEMPLATE.format(slot=self.selected_api_key_index.get()+1) # Use global index for now

            if models and models[0] == api_key_error_msg:
                # API key error occurred during fetch for this provider
                model_to_set = "Brak klucza globalnego" # Specific message for prompt combo
                state_to_set = "disabled"
            elif models and models[0] not in fetch_error_messages:
                # Models loaded successfully
                state_to_set = "readonly"
                if last_selected_model in models and last_selected_model != "Pobieranie...":
                    model_to_set = last_selected_model
                else:
                    model_to_set = models[0] # Default to first model
            else:
                # Other fetch error or empty list
                model_to_set = models[0] if models else NO_MODELS_FOUND # Display fetch error or "No models"
                # state_to_set remains "disabled"

            # 1. Update the variable FIRST
            print(f"[DEBUG] Setting model_name variable for prompt {index+1} to: '{model_to_set}'")
            model_var.set(model_to_set)

            # 2. Configure the widget state and values SECOND
            print(f"[DEBUG] Configuring model_combobox for prompt {index+1}: values='{models[:5]}...', state='{state_to_set}'")
            model_combo.configure(values=models, state=state_to_set)

            print(f"[DEBUG] _update_prompt_combobox_values {index+1}: Final state='{model_combo.cget('state')}', Final variable='{model_var.get()}'")

        except Exception as e:
             print(f"Błąd podczas aktualizacji GUI modeli dla promptu {index+1}: {e}")
             # Attempt to recover gracefully
             try:
                 model_var.set(ERROR_GUI)
                 model_combo.configure(values=[ERROR_GUI], state="disabled")
             except Exception as inner_e:
                 print(f"Błąd podczas obsługi błędu aktualizacji GUI dla promptu {index+1}: {inner_e}")


    # --- Global Model Update Logic (Keep for now, might be removed later) ---
    # Note: This section might become obsolete if global selection is fully removed.

    def update_models_list(self, *args):
        """Updates the GLOBAL model combobox (if it still exists)."""
        # TODO: Decide if this global update is still needed. If not, remove this method
        # and the corresponding button/calls. For now, it might target a non-existent widget.
        print("[WARN] Global update_models_list called - may target non-existent widget.")
        if hasattr(self, 'model_combobox') and self.model_combobox:
             # Restore getting the global provider
             provider = self.selected_provider.get()

             # Disable combobox during update
             self.model_combobox.configure(state="disabled")
             self.model_combobox['values'] = ["Pobieranie..."]
             # self.model_name.set("Pobieranie...") # Uses the *old* global model variable - REMOVED
             self.root.update_idletasks()

             # Run fetching in a separate thread
             threading.Thread(target=self._fetch_and_update_models, daemon=True).start()
        else:
             print("[WARN] Global model_combobox not found for update.")


    def _fetch_and_update_models(self):
        """Helper function to fetch models and update GLOBAL GUI from a thread."""
        # TODO: Decide if this global update is still needed.
        print("[WARN] Global _fetch_and_update_models called.")
        # Restore getting the global provider
        provider = self.selected_provider.get()
        models = self.fetch_models_for_provider(provider)
        self.root.after(0, self._update_combobox_values, models) # Calls the *old* global update function


    def _update_combobox_values(self, models):
         """Updates the GLOBAL combobox values (called from main thread). Simplified logic."""
         # This method updates the GLOBAL combobox.
         print("[DEBUG] Global _update_combobox_values called.")
         if not hasattr(self, 'model_combobox') or not self.model_combobox:
              print("[ERROR] Global model_combobox not found for update.")
              return

         try:
             last_selected_model = self.model_name.get() # Use global variable
             model_to_set = ""
             state_to_set = "disabled" # Default to disabled

             # Use defined constants and template
             error_messages = [ERROR_NO_API_KEY_TEMPLATE.format(slot=i+1) for i in range(NUM_API_KEYS)] + \
                              [ERROR_FETCHING, ERROR_OLLAMA_CONNECTION, NO_MODELS_FOUND, ERROR_GUI, "Pobieranie..."]

             if models and models[0] not in error_messages:
                 # Models loaded successfully
                 state_to_set = "readonly"
                 if last_selected_model in models and last_selected_model != "Pobieranie...":
                     model_to_set = last_selected_model
                 else:
                     model_to_set = models[0] # Default to first model
             else:
                 # Error loading models or empty list
                 model_to_set = models[0] if models else NO_MODELS_FOUND # Display error or "No models"
                 # state_to_set remains "disabled"

             # 1. Update the global variable FIRST
             print(f"[DEBUG] Setting GLOBAL model_name variable to: '{model_to_set}'")
             self.model_name.set(model_to_set) # Update global variable

             # 2. Configure the global widget state and values SECOND
             print(f"[DEBUG] Configuring GLOBAL model_combobox: values='{models[:5]}...', state='{state_to_set}'")
             self.model_combobox.configure(values=models, state=state_to_set)
             print(f"[DEBUG] GLOBAL _update_combobox_values: Final state='{self.model_combobox.cget('state')}', Final variable='{self.model_name.get()}'")

             # Update per-prompt models if their provider matches the global provider
             if state_to_set == "readonly": # Only update if global models loaded successfully
                 current_global_provider = self.selected_provider.get()
                 for i in range(NUM_SYSTEM_PROMPTS):
                     if self.prompt_providers[i].get() == current_global_provider:
                         print(f"[DEBUG] Updating prompt {i+1} model to global: '{model_to_set}'")
                         self.prompt_models[i].set(model_to_set)
                         # Optionally re-configure the per-prompt combo state if needed, but variable update should suffice
                         # if self.prompt_model_combos[i]:
                         #     self.prompt_model_combos[i].configure(state="readonly") # Ensure it's usable

         # Correctly indented except block for the main try
         except Exception as e:
              print(f"Błąd podczas aktualizacji GLOBALNEGO GUI modeli: {e}")
              # Attempt to recover gracefully
              try:
                  self.model_name.set(ERROR_GUI) # Update global variable
                  self.model_combobox.configure(values=[ERROR_GUI], state="disabled")
              except Exception as inner_e:
                  print(f"Błąd podczas obsługi błędu aktualizacji GLOBALNEGO GUI: {inner_e}")

    def get_ollama_endpoint(self):
        """Gets the Ollama endpoint, defaulting to localhost."""
        # In a real app, you might get this from config or another entry field
        return "http://localhost:11434"

    # --- LLM Call Functions ---
    # Note: These functions now retrieve the API key internally using get_selected_api_key()

    def call_openai(self, model, prompt):
        """Calls the OpenAI API."""
        error_messages = [f"Wprowadź klucz API (Slot {i+1})" for i in range(NUM_API_KEYS)] + \
                         ["Błąd pobierania modeli", "Brak modeli", "Pobieranie...", "Błąd GUI"]
        if model in error_messages:
            messagebox.showerror("Błąd Konfiguracji", f"Nieprawidłowy model wybrany: {model}")
            return None
        try:
            api_key = self.get_selected_api_key()
            if not api_key:
                provider = self.selected_provider.get()
                messagebox.showerror("Błąd Konfiguracji", f"Brak klucza API (Slot {self.selected_api_key_index.get()+1}) dla dostawcy: {provider}")
                return None
            client = openai.OpenAI(api_key=api_key)
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}]
            )
            return response.choices[0].message.content.strip()
        except openai.AuthenticationError as e:
             print(f"Błąd autentykacji OpenAI (Slot {self.selected_api_key_index.get()+1}): {e}")
             messagebox.showerror("Błąd Klucza API", f"Nieprawidłowy klucz API dla OpenAI (Slot {self.selected_api_key_index.get()+1}). Sprawdź klucz.")
             return None
        except Exception as e:
            print(f"Błąd OpenAI API: {e}")
            messagebox.showerror("Błąd OpenAI", f"Wystąpił błąd: {e}")
            return None

    def call_anthropic(self, model, system_prompt, user_content):
        """Calls the Anthropic API."""
        # Anthropic doesn't require an API key in the current setup (uses hardcoded models)
        # If you add key support later, use get_selected_api_key() here.
        error_messages = [f"Wprowadź klucz API (Slot {i+1})" for i in range(NUM_API_KEYS)] + \
                         ["Błąd pobierania modeli", "Brak modeli", "Pobieranie...", "Błąd GUI"]
        if model in error_messages:
            messagebox.showerror("Błąd Konfiguracji", f"Nieprawidłowy model wybrany: {model}")
            return None
        try:
            # Assuming you might add API key requirement later
            # api_key = self.get_selected_api_key()
            # if not api_key:
            #     messagebox.showerror("Błąd Konfiguracji", f"Brak klucza API (Slot {self.selected_api_key_index.get()+1}) dla Anthropic")
            #     return None
            # client = anthropic.Anthropic(api_key=api_key) # Uncomment if key needed

            # Using hardcoded models for now, no client needed unless you change this
            # Placeholder for actual API call if implemented
            print("UWAGA: Wywołanie Anthropic API nie jest zaimplementowane - używanie hardkodowanej listy modeli.")
            # Simulating a response for testing purposes if needed
            # return f"Symulowana odpowiedź Anthropic dla modelu {model}"
            messagebox.showinfo("Informacja", "Wywołanie API Anthropic nie jest jeszcze zaimplementowane w tej wersji.")
            return None # Return None as it's not implemented

        except Exception as e:
            print(f"Błąd Anthropic API: {e}")
            messagebox.showerror("Błąd Anthropic", f"Wystąpił błąd: {e}")
            return None

    def call_gemini(self, model, system_prompt, text_content, image_pil=None):
        """Wywołuje API Google Gemini, obsługując tekst i opcjonalne wejście obrazu PIL."""
        api_key = self.get_selected_api_key()
        if not api_key:
            print("[ERROR] Klucz API Google Gemini nie został ustawiony.")
            raise Exception("Klucz API Google Gemini nie został ustawiony.")

        try:
            genai.configure(api_key=api_key)
            gemini_model = genai.GenerativeModel(model_name=model) # Użyj argumentu model_name

            contents = []
            # Połącz prompt systemowy i treść tekstową dla uproszczenia, jeśli oba istnieją
            # Lub obsłuż prompt systemowy osobno, jeśli model/API obsługuje go lepiej
            full_text_prompt = ""
            if system_prompt:
                full_text_prompt += system_prompt + "\n\n"
            if text_content:
                full_text_prompt += text_content

            if full_text_prompt:
                contents.append(full_text_prompt)

            # Dodaj obiekt obrazu PIL, jeśli jest dostępny i model jest multimodalny
            if image_pil and isinstance(image_pil, Image.Image) and self.is_multimodal("Google Gemini", model):
                 print("[DEBUG] Dodawanie obrazu PIL do zapytania Gemini...")
                 contents.append(image_pil)
            elif image_pil and not self.is_multimodal("Google Gemini", model):
                 print(f"[WARN] Obraz dostarczony, ale model Gemini '{model}' nie jest rozpoznany jako multimodalny. Ignorowanie obrazu.")

            if not contents:
                 print("[WARN] Brak zawartości tekstowej i obrazowej do wysłania do Gemini.")
                 return None

            print(f"[DEBUG] Wysyłanie do Gemini: Model={model}, Liczba części={len(contents)}, Obraz obecny={image_pil is not None}")
            # generate_content akceptuje iterowalny obiekt stringów i obrazów PIL
            response = gemini_model.generate_content(contents, stream=False)

            if response and hasattr(response, 'text') and response.text:
                 return response.text.strip()
            # Czasami odpowiedź może być w częściach nawet przy stream=False
            elif response and hasattr(response, 'parts') and response.parts:
                 full_text = "".join(part.text for part in response.parts if hasattr(part, 'text'))
                 return full_text.strip()

            print("[WARN] Otrzymano nieoczekiwaną strukturę odpowiedzi od Gemini lub brak tekstu:", response)
            if response and hasattr(response, 'prompt_feedback'):
                 print(f"[DEBUG] Gemini Prompt Feedback: {response.prompt_feedback}")
            return None
        except Exception as e:
            print(f"Błąd podczas wywołania Google Gemini API: {e}")
            if "API key not valid" in str(e):
                 raise Exception(f"Błąd Google Gemini: Klucz API (Slot {self.selected_api_key_index.get()+1}) nieprawidłowy.") from e
            elif "User location is not supported" in str(e):
                 raise Exception("Błąd Google Gemini: Lokalizacja użytkownika nie jest obsługiwana dla tego API.") from e
            # Obsłuż potencjalne błędy generowania treści (np. filtry bezpieczeństwa)
            elif "response was blocked" in str(e).lower() or "safety settings" in str(e).lower():
                 print(f"[WARN] Odpowiedź Gemini zablokowana przez filtry bezpieczeństwa: {e}")
                 raise Exception(f"Odpowiedź Gemini zablokowana (filtry bezpieczeństwa).") from e
            # Obsłuż potencjalne błędy, jeśli format wejściowy (np. obraz PIL) jest nieprawidłowy dla modelu
            elif "unsupported format" in str(e).lower():
                 print(f"[ERROR] Błąd Gemini: Nieobsługiwany format danych wejściowych (prawdopodobnie obraz): {e}")
                 raise Exception(f"Błąd Gemini: Nieobsługiwany format danych wejściowych (obraz?).") from e
            raise

    def call_ollama(self, model, prompt):
        """Calls the local Ollama API."""
        if model in ["Błąd połączenia z Ollama", "Brak modeli", "Pobieranie...", "Błąd GUI"]:
            messagebox.showerror("Błąd Konfiguracji", f"Nieprawidłowy model wybrany: {model}")
            return None
        try:
            ollama_endpoint = self.get_ollama_endpoint()
            generate_url = f"{ollama_endpoint.rstrip('/')}/api/generate"
            payload = {
                "model": model,
                "prompt": prompt,
                "stream": False # Get the full response at once
            }
            response = requests.post(generate_url, json=payload, timeout=60) # Increased timeout
            response.raise_for_status()
            response_data = response.json()
            return response_data.get("response", "").strip()
        except requests.exceptions.ConnectionError:
             print(f"Błąd połączenia z Ollama: {ollama_endpoint}")
             messagebox.showerror("Błąd Połączenia Ollama", f"Nie można połączyć się z serwerem Ollama pod adresem: {ollama_endpoint}")
             return None
        except Exception as e:
            print(f"Błąd Ollama API: {e}")
            messagebox.showerror("Błąd Ollama", f"Wystąpił błąd: {e}")
            return None

    def call_openrouter(self, model, prompt):
        """Calls the OpenRouter API using OpenAI client."""
        error_messages = [f"Wprowadź klucz API (Slot {i+1})" for i in range(NUM_API_KEYS)] + \
                         ["Błąd pobierania modeli", "Brak modeli", "Pobieranie...", "Błąd GUI"]
        if model in error_messages:
            messagebox.showerror("Błąd Konfiguracji", f"Nieprawidłowy model wybrany: {model}")
            return None
        try:
            api_key = self.get_selected_api_key()
            if not api_key:
                provider = self.selected_provider.get()
                messagebox.showerror("Błąd Konfiguracji", f"Brak klucza API (Slot {self.selected_api_key_index.get()+1}) dla dostawcy: {provider}")
                return None
            client = openai.OpenAI(
                base_url="https://openrouter.ai/api/v1",
                api_key=api_key,
            )
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}]
            )
            return response.choices[0].message.content.strip()
        except openai.AuthenticationError as e:
             print(f"Błąd autentykacji OpenRouter (Slot {self.selected_api_key_index.get()+1}): {e}")
             messagebox.showerror("Błąd Klucza API", f"Nieprawidłowy klucz API dla OpenRouter (Slot {self.selected_api_key_index.get()+1}). Sprawdź klucz.")
             return None
        except Exception as e:
            print(f"Błąd OpenRouter API: {e}")
            messagebox.showerror("Błąd OpenRouter", f"Wystąpił błąd: {e}")
            return None

    def call_deepseek(self, model, prompt):
        """Calls the DeepSeek API using OpenAI client."""
        error_messages = [f"Wprowadź klucz API (Slot {i+1})" for i in range(NUM_API_KEYS)] + \
                         ["Błąd pobierania modeli", "Brak modeli", "Pobieranie...", "Błąd GUI"]
        if model in error_messages:
            messagebox.showerror("Błąd Konfiguracji", f"Nieprawidłowy model wybrany: {model}")
            return None
        try:
            api_key = self.get_selected_api_key()
            if not api_key:
                provider = self.selected_provider.get()
                messagebox.showerror("Błąd Konfiguracji", f"Brak klucza API (Slot {self.selected_api_key_index.get()+1}) dla dostawcy: {provider}")
                return None
            client = openai.OpenAI(
                base_url="https://api.deepseek.com",
                api_key=api_key,
            )
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}]
            )
            return response.choices[0].message.content.strip()
        except openai.AuthenticationError as e:
             print(f"Błąd autentykacji DeepSeek (Slot {self.selected_api_key_index.get()+1}): {e}")
             messagebox.showerror("Błąd Klucza API", f"Nieprawidłowy klucz API dla DeepSeek (Slot {self.selected_api_key_index.get()+1}). Sprawdź klucz.")
             return None
        except Exception as e:
            print(f"Błąd DeepSeek API: {e}")
            messagebox.showerror("Błąd DeepSeek", f"Wystąpił błąd: {e}")
            return None

    # --- Always on Top Window Management ---

    def toggle_on_top_window(self):
        """Creates or destroys the always-on-top window based on the checkbox state."""
        if self.always_on_top_var.get():
            if not self.on_top_window or not self.on_top_window.winfo_exists():
                self.create_on_top_window()
        else:
            if self.on_top_window and self.on_top_window.winfo_exists():
                self.on_top_window.destroy()
                self.on_top_window = None
                self.on_top_text_widget = None

    def create_on_top_window(self):
        """Creates the always-on-top window."""
        self.on_top_window = tk.Toplevel(self.root)
        self.on_top_window.title("Odpowiedź LLM")
        # Użyj zapisanych/domyślnych wymiarów
        geometry_string = f"{self.on_top_window_width}x{self.on_top_window_height}"
        print(f"[DEBUG] Ustawianie geometrii okna 'Zawsze na wierzchu': {geometry_string}")
        self.on_top_window.geometry(geometry_string) # Użyj zmiennych
        self.on_top_window.wm_attributes("-topmost", True)

        self.on_top_text_widget = scrolledtext.ScrolledText(
            self.on_top_window,
            wrap=tk.WORD,
            state=tk.DISABLED, # Tylko do odczytu
            padx=5,
            pady=5,
            font=("Segoe UI", 14) # Zwiększony rozmiar czcionki do 14
        )
        self.on_top_text_widget.pack(fill=tk.BOTH, expand=True)

        # Obsługa zamykania okna "Zawsze na wierzchu" za pomocą przycisku 'X'
        self.on_top_window.protocol("WM_DELETE_WINDOW", self.on_top_window_close)
        # Powiąż zdarzenie zmiany konfiguracji (w tym rozmiaru) z metodą zapisującą wymiary
        self.on_top_window.bind("<Configure>", self.on_top_window_configure)

    def on_top_window_close(self):
        """Called when the always-on-top window is closed manually."""
        if self.on_top_window:
            self.on_top_window.destroy()
        self.on_top_window = None
        self.on_top_text_widget = None
        self.always_on_top_var.set(False) # Odznacz pole wyboru

    def on_top_window_configure(self, event):
        """Wywoływana, gdy okno 'Zawsze na wierzchu' jest konfigurowane (np. zmiana rozmiaru)."""
        # Sprawdź, czy zdarzenie dotyczy głównego okna Toplevel (a nie np. wewnętrznego widżetu)
        # i czy okno nadal istnieje
        if event.widget == self.on_top_window and self.on_top_window and self.on_top_window.winfo_exists():
             # Zapisz nowe wymiary tylko jeśli różnią się od poprzednich (optymalizacja)
             new_width = event.width
             new_height = event.height
             if new_width != self.on_top_window_width or new_height != self.on_top_window_height:
                  self.on_top_window_width = new_width
                  self.on_top_window_height = new_height
                  # Opcjonalnie: logowanie zmiany rozmiaru
                  # print(f"[DEBUG] Zmieniono rozmiar okna 'Zawsze na wierzchu' na: {new_width}x{new_height}")
                  # Opcjonalnie: natychmiastowy zapis konfiguracji (może być zbyt częsty)
                  # self.save_config()
    def on_closing(self):
        """Handles closing the main application window."""
        print("Zamykanie aplikacji...")
        # Stop monitoring if active
        if self.is_monitoring:
            self.is_monitoring = False
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                 print("Oczekiwanie na zakończenie wątku monitorowania...")
                 # Give the thread a moment to finish its current loop
                 # self.monitoring_thread.join(timeout=1.5) # Optional: wait briefly

        # Close the on-top window if it exists
        if self.on_top_window and self.on_top_window.winfo_exists():
            self.on_top_window.destroy()

        # Save configuration before exiting
        self.save_config()

        self.root.destroy()

    def process_agent_request(self, text_content, image_content=None, agent_type="single"):
        """Przetwarza żądanie agenta (@agent lub @super)."""
        print(f"[DEBUG process_agent_request] Typ agenta: {agent_type}")
        print(f"[DEBUG process_agent_request] Zawartość: '{text_content[:50]}...'")

        # Sprawdź czy dostawca to OpenRouter lub DeepSeek
        active_index = self.active_prompt_index.get()
        prompt_provider = self.prompt_providers[active_index].get()

        if prompt_provider not in ["OpenRouter", "DeepSeek"]:
            error_msg = f"Agenty działają tylko z dostawcami OpenRouter i DeepSeek. Aktualny dostawca: {prompt_provider}"
            print(f"[ERROR] {error_msg}")
            self._update_on_top_widget(f"Błąd: {error_msg}")
            messagebox.showerror("Błąd Konfiguracji", error_msg)
            return

        # Przygotuj konfigurację agenta
        try:
            self._update_on_top_widget("Przygotowywanie agenta...")
            self.prepare_agent_config()

            # Uruchom odpowiedni agent
            if agent_type == "single":
                response = self.run_single_agent(text_content, image_content)
            elif agent_type == "multi":
                response = self.run_multi_agent(text_content, image_content)
            else:
                raise ValueError(f"Nieznany typ agenta: {agent_type}")

            if response:
                print(f"Odpowiedź agenta: {response[:100]}...")
                try:
                    pyperclip.copy(response)
                    print("Odpowiedź agenta skopiowana do schowka.")
                    self._update_on_top_widget(response)
                    # Zaktualizuj last_clipboard_content
                    self.last_clipboard_content = response
                    self.last_clipboard_image_hash = None
                except pyperclip.PyperclipException as e:
                    messagebox.showerror("Błąd Schowka", f"Nie można skopiować odpowiedzi do schowka: {e}")
                    self._update_on_top_widget(f"Błąd kopiowania odpowiedzi: {e}")
            else:
                print("Otrzymano pustą odpowiedź od agenta.")
                self._update_on_top_widget("Otrzymano pustą odpowiedź od agenta.")

        except Exception as e:
            error_msg = f"Błąd podczas przetwarzania żądania agenta: {str(e)}"
            print(f"[ERROR] {error_msg}")
            self._update_on_top_widget(f"Błąd: {str(e)}")
            messagebox.showerror("Błąd Agenta", error_msg)

    def prepare_agent_config(self):
        """Przygotowuje konfigurację agenta - kopiuje API key i model z głównej aplikacji."""
        print("[DEBUG] Przygotowywanie konfiguracji agenta...")

        # Pobierz aktualną konfigurację z głównej aplikacji
        active_index = self.active_prompt_index.get()
        api_key = self.get_selected_api_key()
        model = self.prompt_models[active_index].get()
        provider = self.prompt_providers[active_index].get()

        if not api_key:
            raise ValueError("Brak klucza API")

        if not model or model in ["Błąd pobierania modeli", "Brak modeli", "Pobieranie...", "Błąd GUI"]:
            raise ValueError("Nieprawidłowy model")

        # Ścieżka do pliku konfiguracyjnego make-it-heavy
        make_it_heavy_dir = os.path.join(os.path.dirname(__file__), "make-it-heavy")
        config_path = os.path.join(make_it_heavy_dir, "config.yaml")

        if not os.path.exists(make_it_heavy_dir):
            raise ValueError(f"Katalog make-it-heavy nie istnieje: {make_it_heavy_dir}")

        # Przygotuj konfigurację w zależności od dostawcy
        if provider == "OpenRouter":
            config_data = {
                'provider': {'type': 'openrouter'},
                'openrouter': {
                    'api_key': api_key,
                    'base_url': 'https://openrouter.ai/api/v1',
                    'model': model
                }
            }
        elif provider == "DeepSeek":
            config_data = {
                'provider': {'type': 'deepseek'},
                'deepseek': {
                    'api_key': api_key,
                    'base_url': 'https://api.deepseek.com',
                    'model': model
                }
            }
        else:
            raise ValueError(f"Nieobsługiwany dostawca: {provider}")

        # Wspólne ustawienia dla wszystkich dostawców
        config_data.update({
            'system_prompt': self.system_prompts[active_index].get() or "You are a helpful assistant.",
            'agent': {'max_iterations': 10},
            'orchestrator': {
                'parallel_agents': 4,
                'task_timeout': 300,
                'aggregation_strategy': 'consensus',
                'question_generation_prompt': '''You are an orchestrator that needs to create {num_agents} different questions to thoroughly analyze this topic from multiple angles.

Original user query: {user_input}

Generate exactly {num_agents} different, specific questions that will help gather comprehensive information about this topic.
Each question should approach the topic from a different angle (research, analysis, verification, alternatives, etc.).

Return your response as a JSON array of strings, like this:
["question 1", "question 2", "question 3", "question 4"]

Only return the JSON array, nothing else.''',
                'synthesis_prompt': '''You have {num_responses} different AI agents that analyzed the same query from different perspectives.
Your job is to synthesize their responses into ONE comprehensive final answer.

Here are all the agent responses:

{agent_responses}

IMPORTANT: Just synthesize these into ONE final comprehensive answer that combines the best information from all agents.
Do NOT call mark_task_complete or any other tools. Do NOT mention that you are synthesizing multiple responses.
Simply provide the final synthesized answer directly as your response.'''
            },
            'search': {
                'max_results': 5,
                'user_agent': 'Mozilla/5.0 (compatible; Clipboard Agent)'
            }
        })

        # Zapisz konfigurację
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            print(f"[DEBUG] Konfiguracja zapisana do: {config_path}")
        except Exception as e:
            raise ValueError(f"Nie można zapisać konfiguracji: {str(e)}")

    def run_single_agent(self, text_content, image_content=None):
        """Uruchamia pojedynczego agenta (run_agent.py)."""
        print("[DEBUG] Uruchamianie pojedynczego agenta...")

        make_it_heavy_dir = os.path.join(os.path.dirname(__file__), "make-it-heavy")
        agent_script = os.path.join(make_it_heavy_dir, "run_agent.py")

        if not os.path.exists(agent_script):
            raise ValueError(f"Skrypt run_agent.py nie istnieje: {agent_script}")

        try:
            # Przygotuj input dla agenta
            agent_input = text_content
            if image_content:
                # TODO: Obsługa obrazów w agentach (jeśli potrzebna)
                print("[DEBUG] Obrazy nie są jeszcze obsługiwane przez agentów")

            # Uruchom agenta z query jako argument
            process = subprocess.Popen(
                [sys.executable, agent_script, "--query", agent_input],
                cwd=make_it_heavy_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='replace'  # Zastąp nieobsługiwane znaki
            )

            # Poczekaj na zakończenie
            stdout, stderr = process.communicate(timeout=300)  # 5 minut timeout

            if process.returncode != 0:
                raise ValueError(f"Agent zakończył się błędem: {stderr}")

            # Odpowiedź agenta to cały stdout (bez potrzeby parsowania)
            response = stdout.strip()
            return response if response else "Nie otrzymano odpowiedzi od agenta."

        except subprocess.TimeoutExpired:
            process.kill()
            raise ValueError("Agent przekroczył limit czasu (5 minut)")
        except Exception as e:
            raise ValueError(f"Błąd uruchamiania agenta: {str(e)}")

    def run_multi_agent(self, text_content, image_content=None):
        """Uruchamia multiagenta (run_multi_agent.py wrapper)."""
        print("[DEBUG] Uruchamianie multiagenta...")

        make_it_heavy_dir = os.path.join(os.path.dirname(__file__), "make-it-heavy")
        multi_script = os.path.join(make_it_heavy_dir, "run_multi_agent.py")

        if not os.path.exists(multi_script):
            raise ValueError(f"Skrypt run_multi_agent.py nie istnieje: {multi_script}")

        try:
            # Przygotuj input dla multiagenta
            agent_input = text_content
            if image_content:
                # TODO: Obsługa obrazów w multiagentach (jeśli potrzebna)
                print("[DEBUG] Obrazy nie są jeszcze obsługiwane przez multiagentów")

            # Uruchom multiagenta z query jako argument
            process = subprocess.Popen(
                [sys.executable, multi_script, "--query", agent_input],
                cwd=make_it_heavy_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='replace',  # Zastąp nieobsługiwane znaki
                env={**os.environ, 'PYTHONIOENCODING': 'utf-8'}  # Wymuś UTF-8 dla Python subprocess
            )

            # Poczekaj na zakończenie z timeout
            stdout, stderr = process.communicate(timeout=600)  # 10 minut timeout dla multiagenta

            print(f"[DEBUG] Multiagent return code: {process.returncode}")
            print(f"[DEBUG] Multiagent stdout length: {len(stdout) if stdout else 0}")
            print(f"[DEBUG] Multiagent stderr length: {len(stderr) if stderr else 0}")

            if process.returncode != 0:
                print(f"[DEBUG] Multiagent stderr: {stderr}")
                raise ValueError(f"Multiagent zakończył się błędem: {stderr}")

            # Odpowiedź multiagenta to cały stdout (bez potrzeby parsowania)
            response = stdout.strip()
            print(f"[DEBUG] Multiagent response preview: {response[:200]}...")
            return response if response else "Nie otrzymano odpowiedzi od multiagenta."

        except subprocess.TimeoutExpired:
            process.kill()
            raise ValueError("Multiagent przekroczył limit czasu (10 minut)")
        except Exception as e:
            raise ValueError(f"Błąd uruchamiania multiagenta: {str(e)}")

    def extract_agent_response(self, stdout):
        """Wyodrębnia odpowiedź agenta z output konsoli."""
        lines = stdout.split('\n')
        response_lines = []
        capturing = False

        for line in lines:
            # Szukaj początku odpowiedzi agenta
            if "Agent:" in line and not capturing:
                capturing = True
                # Dodaj część po "Agent:"
                agent_part = line.split("Agent:", 1)
                if len(agent_part) > 1:
                    response_part = agent_part[1].strip()
                    if response_part and response_part != "Thinking...":
                        response_lines.append(response_part)
                continue

            # Jeśli już przechwytujemy, dodawaj linie do odpowiedzi
            if capturing:
                # Zatrzymaj się na kolejnym promptcie użytkownika lub końcu
                if line.strip().startswith("User:") or line.strip() in ["Goodbye!", "Exiting..."]:
                    break
                # Pomiń puste linie na początku
                if line.strip() or response_lines:
                    response_lines.append(line)

        # Połącz linie i oczyść
        response = '\n'.join(response_lines).strip()

        # Jeśli nie znaleziono odpowiedzi w standardowy sposób, spróbuj alternatywnej metody
        if not response:
            # Szukaj ostatniej sekcji z wynikami
            if "FINAL RESULTS" in stdout:
                parts = stdout.split("FINAL RESULTS")
                if len(parts) > 1:
                    final_part = parts[-1]
                    # Usuń separatory i pobierz czystą odpowiedź
                    lines = final_part.split('\n')
                    clean_lines = []
                    for line in lines:
                        if line.strip() and not line.strip().startswith('='):
                            clean_lines.append(line.strip())
                    response = '\n'.join(clean_lines).strip()

        return response if response else "Nie otrzymano odpowiedzi od agenta."

    def handle_query_keypress(self, event):
        """Obsługuje naciśnięcia klawiszy w polu ZAPYTANIE.
        Enter: Kopiuje zawartość do schowka i czyści pole.
        Shift+Enter: Wstawia nową linię (domyślne zachowanie).
        """
        # Sprawdź, czy naciśnięto Enter
        if event.keysym == 'Return':
            # Sprawdź, czy Shift NIE jest wciśnięty (maska 0x0001 dla Shift)
            if not (event.state & 0x0001):
                content = self.query_textbox.get("0.0", "end-1c") # Pobierz tekst bez końcowej nowej linii
                if content: # Kopiuj tylko jeśli jest zawartość
                    try:
                        pyperclip.copy(content)
                        print(f"Skopiowano do schowka: {content[:50]}...") # Logowanie
                    except Exception as e:
                        print(f"Błąd kopiowania do schowka: {e}")
                        # Można rozważyć pokazanie błędu użytkownikowi
                        # messagebox.showerror("Błąd", f"Nie można skopiować do schowka: {e}")

                # Wyczyść pole tekstowe
                self.query_textbox.delete("0.0", "end")

                # Zwróć "break", aby zapobiec domyślnej akcji Enter (wstawienie nowej linii)
                return "break"
            # Jeśli Shift jest wciśnięty, pozwól na domyślną akcję (nowa linia)
            # Nie zwracaj "break"
        # Dla innych klawiszy, pozwól na domyślną akcję
        return None


if __name__ == "__main__":
    # root = tk.Tk() # Original Tkinter root
    root = customtkinter.CTk() # Use CustomTkinter root
    app = ClipboardLLMAutomator(root)
    root.mainloop()

# Clipboard LLM Automator

A powerful GUI application that monitors your system clipboard for text and image changes, automatically processing them through various Large Language Model (LLM) APIs including OpenAI, Anthropic, Google Gemini, Ollama, OpenRouter, and DeepSeek. Features integrated multi-agent system for complex analysis tasks.

## Features

- **Real-time Clipboard Monitoring**: Automatically detects text and image changes in your clipboard
- **Multi-LLM Support**: Works with OpenAI GPT, Anthropic Claude, Google Gemini, local Ollama models, OpenRouter, and DeepSeek
- **Multimodal Processing**: Supports both text and image inputs for compatible models
- **Multiple System Prompts**: Configure up to 5 different system prompts with individual LLM settings
- **Always-on-Top Window**: Optional floating window to display LLM responses
- **Agent Commands**: Special @agent and @super commands for intelligent task automation
- **Multi-Agent System**: Integrated make-it-heavy framework for complex analysis with parallel agents
- **Cross-Platform**: Works on Windows and macOS
- **Secure API Key Management**: Multiple API key slots per provider for redundancy

## Installation

### Prerequisites

- Python 3.8 or higher
- pip package manager

### Quick Install

1. Clone the repository:
```bash
git clone https://github.com/yourusername/clipboard-llm-automator.git
cd clipboard-llm-automator
```

2. Set up development environment (recommended):

**Windows:**
```batch
scripts\setup_dev_environment.bat
```

**macOS/Linux:**
```bash
chmod +x scripts/setup_dev_environment.sh
./scripts/setup_dev_environment.sh
```

3. Activate virtual environment:

**Windows:**
```batch
venv\Scripts\activate.bat
```

**macOS/Linux:**
```bash
source venv/bin/activate
```

4. Run the application:
```bash
python clipboard.py
```

### Manual Install (if you prefer not to use the setup script)

1. Create virtual environment:
```bash
python -m venv venv
```

2. Activate virtual environment:
```bash
# Windows
venv\Scripts\activate.bat

# macOS/Linux
source venv/bin/activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. For development with additional tools:
```bash
pip install -e ".[dev]"
```

## Usage

### Basic Setup

1. **Launch the application**:
   ```bash
   python clipboard.py
   ```

2. **Configure LLM Provider**:
   - Select your preferred LLM provider from the dropdown
   - Enter your API key in one of the available slots
   - Click "Zapisz Klucz" to save the API key

3. **Set System Prompts**:
   - Configure up to 5 different system prompts
   - Each prompt can use a different LLM provider and model
   - Click on a prompt text area to make it active

4. **Start Monitoring**:
   - Click "Start Monitorowania" to begin clipboard monitoring
   - Copy text or images to your clipboard
   - The application will automatically process the content and copy the LLM response back to your clipboard

### Agent Commands

The application includes powerful agent commands for intelligent task automation:

#### @agent Command (Single Agent Mode)
- **Usage**: Copy text starting with `@agent ` to clipboard
- **Function**: Runs a single intelligent agent with full tool access (web search, calculations, etc.)
- **Supported Providers**: OpenRouter, DeepSeek
- **Example**: Copy `@agent Research the latest AI developments and summarize them` to clipboard

#### @super Command (Multi-Agent Mode)
- **Usage**: Copy text starting with `@super ` to clipboard
- **Function**: Runs 4 parallel agents for comprehensive analysis (Grok Heavy-style)
- **Supported Providers**: OpenRouter, DeepSeek
- **Example**: Copy `@super Analyze the impact of AI on software development` to clipboard

**Note**: Agent commands automatically use the provider and model configured in your active prompt slot.

### API Keys

The application supports multiple API key slots for each provider:

- **OpenAI**: Requires API key from [OpenAI Platform](https://platform.openai.com/)
- **Anthropic**: Requires API key from [Anthropic Console](https://console.anthropic.com/)
- **Google Gemini**: Requires API key from [Google AI Studio](https://makersuite.google.com/)
- **OpenRouter**: Requires API key from [OpenRouter](https://openrouter.ai/)
- **DeepSeek**: Requires API key from [DeepSeek Platform](https://platform.deepseek.com/)
- **Ollama**: No API key required (local installation needed)

### Supported Models

The application automatically detects available models for each provider. Multimodal models (supporting images) include:

- **OpenAI**: GPT-4 Vision, GPT-4 Turbo, GPT-4o
- **Google Gemini**: All Gemini models
- **Anthropic**: Claude 3 family
- **DeepSeek**: DeepSeek-V3, DeepSeek-R1 (text only)
- **Ollama**: LLaVA, BakLLaVA (and other vision models)

## Building for Distribution

### 🎯 **Professional Windows Installer (Recommended)**

For easy distribution to end users, create a professional installer that automatically handles all dependencies:

```batch
# One-command installer build (includes everything)
scripts\build_installer.bat
```

**What you get:**
- ✅ Professional Windows installer (.exe)
- ✅ Automatic Visual C++ Redistributable installation
- ✅ System requirements check
- ✅ Desktop and Start Menu shortcuts
- ✅ Complete documentation included
- ✅ Works on any Windows 10/11 system

**Requirements:** [Inno Setup 6](https://jrsoftware.org/isdl.php) (free download)

See [INSTALLER_SETUP.md](INSTALLER_SETUP.md) for detailed instructions.

### 📦 **Standalone Executable**

For simple distribution without installer:

```batch
# Make sure virtual environment is set up
scripts\setup_dev_environment.bat

# Standard build
scripts\build_windows.bat

# Enhanced build (better compatibility)
scripts\build_windows_enhanced.bat
```

**Compatibility:** Works on Windows 10/11 (64-bit). See [COMPATIBILITY.md](COMPATIBILITY.md) for troubleshooting.

### macOS

**⚠️ Important:** macOS executables must be built ON macOS (cross-compilation not supported).

```bash
# Make sure virtual environment is set up
./scripts/setup_dev_environment.sh

# Build app bundle
./scripts/build_macos.sh
```

**Requirements:** macOS 11+ (Big Sur), Xcode Command Line Tools

### Manual Build (Advanced)

If you prefer to build manually:

```bash
# Activate virtual environment first
# Windows: venv\Scripts\activate.bat
# macOS/Linux: source venv/bin/activate

# Install PyInstaller if not already installed
pip install pyinstaller

# Build using spec file
pyinstaller clipboard.spec

# Or build directly
pyinstaller --windowed --onefile --name "Clipboard LLM Automator" src/main.py
```

## Distribution & Compatibility

### 🚀 **Recommended: Professional Installer**

**Best for end users:** Use the Windows installer for hassle-free distribution:

```batch
scripts\build_installer.bat
```

**✅ Installer advantages:**
- **Zero manual setup** - Everything installed automatically
- **Professional appearance** - Modern Windows installer wizard
- **Dependency management** - Visual C++ Redistributable auto-installed
- **System validation** - Checks Windows 10/11 compatibility
- **Easy uninstall** - Through Windows Settings
- **Documentation included** - All guides accessible from Start Menu

**✅ Works perfectly on:**
- Windows 10 (version 1809+) 64-bit
- Windows 11 (all versions) 64-bit
- Clean systems without Python/development tools
- Corporate environments with restricted permissions

### 📦 **Alternative: Standalone EXE**

**For advanced users:** Direct executable distribution:

**✅ Should work on:**
- Windows 10 (version 1903+) 64-bit
- Windows 11 (all versions) 64-bit
- Computers without Python installed

**❌ Won't work on:**
- Windows 7, 8, 8.1 (outdated)
- 32-bit Windows systems
- Very old Windows 10 versions

**If EXE doesn't work on other computers:**
1. User may need [Visual C++ Redistributable](https://aka.ms/vs/17/release/vc_redist.x64.exe)
2. Windows Defender might block it (right-click → "Run anyway")
3. Use the installer instead: `scripts\build_installer.bat`

See [COMPATIBILITY.md](COMPATIBILITY.md) for detailed troubleshooting.

### macOS App Distribution

**Requirements for building:**
- Must be compiled ON macOS (not Windows)
- macOS 11+ (Big Sur or later)
- Xcode Command Line Tools

**Distribution challenges:**
- Gatekeeper blocks unsigned apps
- Users need to right-click → "Open" first time
- Consider Apple Developer Account for code signing

## Configuration

The application saves configuration in `config.json`:

- API keys (encrypted storage recommended for production)
- Selected providers and models
- System prompts
- Window preferences

## Virtual Environment Management

This project uses Python virtual environments to isolate dependencies. Here are some useful commands:

### Activating the Environment

**Windows:**
```batch
venv\Scripts\activate.bat
```

**macOS/Linux:**
```bash
source venv/bin/activate
```

### Deactivating the Environment

```bash
deactivate
```

### Updating Dependencies

```bash
# Activate environment first, then:
pip install --upgrade -r requirements.txt
```

### Adding New Dependencies

```bash
# Install new package
pip install package_name

# Update requirements.txt
pip freeze > requirements.txt
```

## Troubleshooting

### Common Issues

1. **"Wprowadź klucz API" error**: Ensure you've entered and saved a valid API key
2. **No models found**: Check your internet connection and API key validity
3. **Image processing fails**: Ensure you're using a multimodal model
4. **Clipboard not detected**: Try copying content again or restart the application
5. **Module not found errors**: Make sure virtual environment is activated and dependencies are installed
6. **Permission errors on macOS**: You may need to grant accessibility permissions to Terminal or the built app

### Virtual Environment Issues

1. **Virtual environment not found**: Run the setup script first (`scripts/setup_dev_environment.bat` or `./scripts/setup_dev_environment.sh`)
2. **Dependencies not found**: Make sure virtual environment is activated before running the application
3. **Build fails**: Ensure virtual environment is activated and all dependencies are installed

### Logs

The application prints debug information to the console. Run from terminal to see detailed logs.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Built with [CustomTkinter](https://github.com/TomSchimansky/CustomTkinter) for modern GUI
- Uses [Pillow](https://python-pillow.org/) for image processing
- Integrates with multiple LLM providers for maximum flexibility
